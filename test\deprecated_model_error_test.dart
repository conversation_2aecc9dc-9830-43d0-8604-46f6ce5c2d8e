import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Deprecated Model Error Detection', () {
    test('should detect deprecated Gemini model errors', () {
      // Test cases for deprecated model error detection
      final testCases = [
        'HTTP 400: {"error": {"code": 400, "message": "The model `models/gemini-1.0-pro` has been deprecated", "status": "INVALID_ARGUMENT"}}',
        'Gemini model deprecated: gemini-1.0-pro is no longer supported',
        'Model gemini-1.0-pro has been deprecated on Jul 12, 2024',
        'HTTP 400: model has been deprecated',
        'The Gemini model you are using is deprecated',
      ];

      for (final errorMessage in testCases) {
        final isDeprecated = _isDeprecatedModelError(errorMessage);
        expect(isDeprecated, true, reason: 'Should detect: $errorMessage');
      }
    });

    test('should not detect non-deprecated errors as deprecated', () {
      final testCases = [
        'HTTP 401: Unauthorized',
        'Network connection failed',
        'Invalid API key',
        'Rate limit exceeded',
        'OpenAI API error',
      ];

      for (final errorMessage in testCases) {
        final isDeprecated = _isDeprecatedModelError(errorMessage);
        expect(isDeprecated, false, reason: 'Should not detect: $errorMessage');
      }
    });
  });
}

// Copy of the detection logic for testing
bool _isDeprecatedModelError(dynamic error) {
  final errorStr = error.toString().toLowerCase();
  return errorStr.contains('gemini') && 
         (errorStr.contains('deprecated') || 
          errorStr.contains('no longer supported') ||
          errorStr.contains('gemini-1.0-pro') ||
          errorStr.contains('model has been deprecated'));
}
