import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Gemini Error Detection', () {
    test('should detect deprecated Gemini model errors', () {
      // Test cases for deprecated model error detection
      final testCases = [
        'HTTP 400: {"error": {"code": 400, "message": "The model `models/gemini-1.0-pro` has been deprecated", "status": "INVALID_ARGUMENT"}}',
        'Gemini model deprecated: gemini-1.0-pro is no longer supported',
        'Model gemini-1.0-pro has been deprecated on Jul 12, 2024',
        'HTTP 400: model has been deprecated',
        'The Gemini model you are using is deprecated',
      ];

      for (final errorMessage in testCases) {
        final isDeprecated = _isDeprecatedModelError(errorMessage);
        expect(isDeprecated, true, reason: 'Should detect: $errorMessage');
      }
    });

    test('should detect Gemini quota exceeded errors', () {
      final testCases = [
        'HTTP 429: {"error": {"code": 429, "message": "Quota exceeded", "status": "RESOURCE_EXHAUSTED"}}',
        'Gemini API quota exceeded for your project',
        'You have exceeded your current quota, please check your plan and billing details',
        'Rate limit exceeded for Gemini API',
        'RESOURCE_EXHAUSTED: Quota exceeded',
        'Billing account required for Gemini API',
      ];

      for (final errorMessage in testCases) {
        final isQuotaExceeded = _isQuotaExceededError(errorMessage);
        expect(isQuotaExceeded, true, reason: 'Should detect: $errorMessage');
      }
    });

    test('should not detect non-Gemini errors', () {
      final testCases = [
        'HTTP 401: Unauthorized',
        'Network connection failed',
        'Invalid API key',
        'OpenAI API error',
        'Claude API rate limit',
      ];

      for (final errorMessage in testCases) {
        final isDeprecated = _isDeprecatedModelError(errorMessage);
        final isQuotaExceeded = _isQuotaExceededError(errorMessage);
        expect(isDeprecated, false, reason: 'Should not detect deprecated: $errorMessage');
        expect(isQuotaExceeded, false, reason: 'Should not detect quota: $errorMessage');
      }
    });
  });
}

// Copy of the detection logic for testing
bool _isDeprecatedModelError(dynamic error) {
  final errorStr = error.toString().toLowerCase();
  return errorStr.contains('gemini') &&
         (errorStr.contains('deprecated') ||
          errorStr.contains('no longer supported') ||
          errorStr.contains('gemini-1.0-pro') ||
          errorStr.contains('model has been deprecated'));
}

bool _isQuotaExceededError(dynamic error) {
  final errorStr = error.toString().toLowerCase();
  return errorStr.contains('gemini') &&
         (errorStr.contains('quota') ||
          errorStr.contains('resource_exhausted') ||
          errorStr.contains('rate limit') ||
          errorStr.contains('billing') ||
          errorStr.contains('exceeded your current quota'));
}
