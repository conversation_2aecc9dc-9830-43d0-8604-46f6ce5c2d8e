import 'openai_client.dart';
import 'claude_client.dart';
import 'deepseek_client.dart';
import 'base_llm_client.dart';
import 'ollama_client.dart';
import 'gemini_client.dart';
import 'foundry_client.dart';
import 'package:bervin/provider/provider_manager.dart';
import 'package:bervin/provider/settings_provider.dart';
import 'package:logging/logging.dart';
import 'model.dart' as llm_model;

enum LLMProvider { openai, claude, ollama, deepseek, gemini, foundry }

class LLMFactory {
  static BaseLLMClient create(
    LLMProvider provider, {
    required String apiKey,
    required String baseUrl,
    String? apiVersion,
  }) {
    switch (provider) {
      case LLMProvider.openai:
        return OpenAIClient(apiKey: apiKey, baseUrl: baseUrl);
      case LLMProvider.claude:
        return ClaudeClient(apiKey: apiKey, baseUrl: baseUrl);
      case LLMProvider.deepseek:
        return DeepSeekClient(apiKey: apiKey, baseUrl: baseUrl);
      case LLMProvider.ollama:
        return OllamaClient(baseUrl: baseUrl);
      case LLMProvider.gemini:
        return GeminiClient(apiKey: apiKey, baseUrl: baseUrl);
      case LLMProvider.foundry:
        return FoundryClient(
          apiKey: apiKey,
          baseUrl: baseUrl,
          apiVersion: apiVersion,
        );
    }
  }
}

class LLMFactoryHelper {
  static final nonChatModelKeywords = {"whisper", "tts", "dall-e", "embedding"};

  static bool isChatModel(llm_model.Model model) {
    return !nonChatModelKeywords.any((keyword) => model.name.contains(keyword));
  }

  static final Map<String, LLMProvider> providerMap = {
    "openai": LLMProvider.openai,
    "claude": LLMProvider.claude,
    "deepseek": LLMProvider.deepseek,
    "ollama": LLMProvider.ollama,
    "gemini": LLMProvider.gemini,
    "foundry": LLMProvider.foundry,
  };

  static String _maskApiKey(String apiKey) {
    if (apiKey.isEmpty) return 'empty';
    if (apiKey.length <= 8) return '***';
    return '${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}';
  }

  static void _logApiKeyUsage(String provider, String model, String apiKey) {
    final maskedKey = _maskApiKey(apiKey);
    Logger.root.info(
      'Using API key for provider: $provider, model: $model, key: $maskedKey',
    );
  }

  static BaseLLMClient createFromModel(llm_model.Model currentModel) {
    try {
      final setting = ProviderManager.settingsProvider.apiSettings.firstWhere(
        (element) => element.providerId == currentModel.providerId,
      );

      // 检查提供商是否启用（null 表示启用，只有 false 为禁用）
      final isEnabled = setting.enable ?? true;
      if (!isEnabled) {
        throw Exception('Provider ${currentModel.providerId} is disabled');
      }

      // Set apiKey and baseUrl
      final apiKey = setting.apiKey;
      final baseUrl = setting.apiEndpoint;

      _logApiKeyUsage(currentModel.providerId, currentModel.name, apiKey);

      var provider = LLMFactoryHelper.providerMap[currentModel.providerId];

      provider ??= LLMProvider.values.byName(currentModel.apiStyle);

      // Create LLM client
      return LLMFactory.create(provider, apiKey: apiKey, baseUrl: baseUrl);
    } catch (e) {
      // If no matching provider is found, use default OpenAI
      Logger.root.warning(
        'No matching provider found: ${currentModel.providerId}, using default OpenAI configuration',
      );

      // Try to get an enabled OpenAI provider as fallback
      var openAISetting = ProviderManager.settingsProvider.getEnabledProviderSetting("openai");

      // If OpenAI is also disabled, create a default setting (this will likely fail but provides better error handling)
      openAISetting ??= LLMProviderSetting(
        apiKey: '',
        apiEndpoint: '',
        providerId: 'openai',
      );

      return OpenAIClient(
        apiKey: openAISetting.apiKey,
        baseUrl: openAISetting.apiEndpoint,
      );
    }
  }
}
