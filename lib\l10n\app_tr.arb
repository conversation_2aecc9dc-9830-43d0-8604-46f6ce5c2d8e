{"@@locale": "tr", "settings": "<PERSON><PERSON><PERSON>", "general": "<PERSON><PERSON>", "providers": "Sağlayıcılar", "mcpServer": "MCP Sunucusu", "language": "Dil", "theme": "<PERSON><PERSON>", "dark": "<PERSON><PERSON>", "light": "Açık", "system": "Sistem", "languageSettings": "<PERSON><PERSON>ı", "featureSettings": "Özellik Ayarları", "enableArtifacts": "Artifact'<PERSON><PERSON>", "enableArtifactsDescription": "<PERSON><PERSON>bette yapay zeka asistanının Artifact'lerini et<PERSON>tirir, bu özellik daha fazla token kullanır.", "enableToolUsage": "<PERSON>ç <PERSON>ımını Etkinleştir", "enableToolUsageDescription": "<PERSON><PERSON><PERSON>te araçların kullanımını etkinleştirir, bu özellik daha fazla token kullanır.", "themeSettings": "<PERSON>ma <PERSON>", "lightTheme": "<PERSON><PERSON>ık <PERSON>", "darkTheme": "<PERSON><PERSON>", "followSystem": "Sistem Ayarlarına <PERSON>", "showAvatar": "Avatar <PERSON>", "showAssistantAvatar": "Asistan Avatarını Göster", "showAssistantAvatarDescription": "So<PERSON>bette yapay zeka asistanının avatarını gösterir.", "showUserAvatar": "Kullanıcı Avatarını Göster", "showUserAvatarDescription": "So<PERSON>bette kullanıcının avatarını gösterir.", "systemPrompt": "Sistem Prompt", "systemPromptDescription": "<PERSON><PERSON>, yapay zeka asistanının davranışını ve tarzını belirlemek için kullanılan sistem yönergesidir.", "llmKey": "LLM Anahtarı", "toolKey": "<PERSON><PERSON>", "saveSettings": "Ayarları Kaydet", "apiKey": "API Anahtarı", "enterApiKey": "{provider} API Anahtarınızı Girin", "apiKeyValidation": "API Anahtarı en az 10 karakter olmalıdır.", "apiEndpoint": "API Uç Noktası", "enterApiEndpoint": "API uç noktası URL'sini girin", "apiVersion": "API Versiyonu", "enterApiVersion": "API versiyonunu girin", "platformNotSupported": "Mevcut platform MCP Sunucusunu desteklemiyor.", "mcpServerDesktopOnly": "MCP Sunucusu yalnızca masaüstü platformlarını (Windows, macOS, Linux) destekler.", "searchServer": "<PERSON><PERSON><PERSON> ara...", "noServerConfigs": "<PERSON><PERSON>u yapılandırması bulunamadı.", "addProvider": "Sağlayıcı Ekle", "refresh": "<PERSON><PERSON><PERSON>", "install": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "command": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "arguments": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "environmentVariables": "<PERSON><PERSON>", "serverName": "<PERSON><PERSON><PERSON>", "commandExample": "Örneğin: npx, uvx, https://mcpserver.com", "argumentsExample": "Argümanları boşlukla ayırın. Boşluk içerenler için tırnak işareti kullanın, örn: -y obsidian-mcp '/Kullanıcılar/kullaniciadi/Belgeler/Obsidian Kasası'", "envVarsFormat": "Her satıra bir tane, ANAHTAR=DEĞER formatında", "cancel": "İptal", "save": "<PERSON><PERSON>", "confirmDelete": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteServer": "\"{name}\" ad<PERSON><PERSON> sun<PERSON><PERSON>u silmek istediğinize emin misiniz?", "error": "<PERSON><PERSON>", "commandNotExist": "\"{command}\" komutu bulunamadı. Lütfen önce bu komutu yükleyin.\n\nMevcut PATH:\n{path}", "all": "Tümü", "installed": "Yüklü", "modelSettings": "<PERSON>", "temperature": "Temperature: {value}", "temperatureTooltip": "Temperature , çıktının rastgeleliğini kontrol eder:\n• 0.0: Kod üretimi ve matematiksel problemler için idealdir.\n• 1.0: Veri çıkarma ve analiz için uygundur.\n• 1.3: Genel sohbet ve çeviri için uygundur.\n• 1.5: Yaratı<PERSON>ı yazarlık ve şiir için harikadır.", "topP": "<PERSON><PERSON><PERSON><PERSON> (Top P): {value}", "topPTooltip": "Top P (Çekirdek Örnekleme), sıcaklık parametresine bir alternatiftir. Model, yalnızca kümülatif olasılığı P'yi aşan token'ları dikkate alır. Sıcaklık ve top_p değerlerini aynı anda değiştirmeniz önerilmez.", "maxTokens": "<PERSON><PERSON><PERSON><PERSON>", "maxTokensTooltip": "Üretilecek maksimum token sayısı. Bir token yaklaşık 4 karaktere eşittir. Daha uzun sohbetler daha fazla token gerektirir.", "frequencyPenalty": "Frequency Cezası: {value}", "frequencyPenaltyTooltip": "Frequency cezası parametresi. <PERSON><PERSON><PERSON><PERSON>, metindeki mevcut frekanslarına göre yeni token'ları cezalandırarak modelin aynı içeriği kelimesi kelimesine tekrarlama olasılığını azaltır.", "presencePenalty": "Presence Cezası: {value}", "presencePenaltyTooltip": "Presence cezası parametresi. <PERSON><PERSON><PERSON><PERSON>, metinde daha önce geçip geçmediklerine göre yeni token'ları cezalandırarak modelin yeni konular hakkında konuşma olasılığını artırır.", "enterMaxTokens": "Maks<PERSON><PERSON> token sayısını girin", "share": "Paylaş", "modelConfig": "<PERSON>", "debug": "Debug Modu", "webSearchTest": "Web Araması Testi", "today": "<PERSON><PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "last7Days": "Son 7 Gün", "last30Days": "Son 30 Gün", "earlier": "<PERSON><PERSON>", "confirmDeleteSelected": "Seçili sohbetleri silmek istediğinize emin misiniz?", "confirmThisChat": "<PERSON>u sohbetleri silmek istediğinize emin misiniz?", "ok": "<PERSON><PERSON>", "askMeAnything": "Aklındakini sor...", "uploadFiles": "<PERSON><PERSON><PERSON>", "welcomeMessage": "Bugün sana nasıl yardımcı olabilirim?", "copy": "Kopyala", "copied": "Panoya kopyalandı!", "retry": "<PERSON><PERSON><PERSON>", "brokenImage": "Bozuk Resim", "toolCall": "{name} çağrılıyor", "toolResult": "{name} ç<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "selectModel": "<PERSON> <PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "selectFromGallery": "Gale<PERSON><PERSON>", "selectFile": "<PERSON><PERSON><PERSON>", "uploadFile": "<PERSON><PERSON><PERSON>", "openBrowser": "Tarayıcıda Aç", "codeCopiedToClipboard": "Kod panoya kopyalandı.", "thinking": "Düşünüyor...", "thinkingEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>.", "tool": "<PERSON><PERSON>", "userCancelledToolCall": "Araç çalıştırılamadı.", "code": "Kod", "preview": "<PERSON><PERSON><PERSON><PERSON>", "functionCallAuth": "<PERSON><PERSON> İzni", "allowFunctionExecution": "Aşağıdaki aracın çalışmasına izin veriyor musunuz?", "parameters": "Parametreler: {params}", "allow": "<PERSON><PERSON> V<PERSON>", "loadContentFailed": "İçerik <PERSON>ü<PERSON>medi, lütfen tekrar den<PERSON>.", "loadDiagramFailed": "Diyagram yüklenemedi, lütfen tekrar den<PERSON>in.", "copiedToClipboard": "Panoya kopyalandı.", "chinese": "<PERSON><PERSON><PERSON>", "turkish": "Türkçe", "functionRunning": "Araç çalıştırılıyor...", "thinkingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ...", "thinkingProcessWithDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, geçen süre", "thinkingEndWithDuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ge<PERSON>en süre", "thinkingEndComplete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seconds": "{seconds}sn", "fieldRequired": "<PERSON><PERSON> <PERSON><PERSON>.", "autoApprove": "Otomatik <PERSON>", "verify": "Anahtarı Doğrula", "howToGet": "Na<PERSON>ıl alınır?", "modelList": "Model Listesi", "enableModels": "<PERSON><PERSON><PERSON> E<PERSON>kinleştir", "disableAllModels": "Tüm Modelleri Devre Dışı Bırak", "saveSuccess": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "genTitleModel": "Başlık Oluşturma Modeli", "serverNameTooLong": "Sunucu adı 50 karakterden uzun olamaz.", "serverType": "<PERSON><PERSON><PERSON>", "reset": "Sıfırla", "start": "<PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON>", "search": "Ara", "newVersionFound": "<PERSON><PERSON> bir {version} sürümü mevcut!", "newVersionAvailable": "<PERSON><PERSON>", "updateNow": "<PERSON><PERSON><PERSON>", "updateLater": "<PERSON><PERSON>", "ignoreThisVersion": "<PERSON><PERSON>ü<PERSON>ü<PERSON>", "releaseNotes": "<PERSON><PERSON>rüm <PERSON>:", "openUrlFailed": "Bağlantı açılamadı.", "checkingForUpdates": "Güncellemeler kontrol ediliyor...", "checkUpdate": "Güncellemeleri Kontrol Et", "appDescription": "ChatMCP, yapay zekayı daha fazla insana ulaştırmayı hedefleyen, platformlar arası bir yapay zeka istemcisidir.", "visitWebsite": "Web Sitesi", "aboutApp": "Hakkında", "networkError": "<PERSON>ğ bağlantı hatası. Lütfen internetinizi kontrol edip tekrar deneyin.", "noElementError": "Eşleşen içerik bulunamadı, lütfen tekrar deneyin.", "permissionError": "Yetersiz izin. Lütfen ayarlarınızı kontrol edin.", "unknownError": "Bilinmeyen bir hata o<PERSON>.", "timeoutError": "İstek zaman aşımına uğradı. Lütfen bir süre sonra tekrar deneyin.", "notFoundError": "İstenen kaynak bulunamadı.", "invalidError": "Geçersiz istek veya parametre.", "unauthorizedError": "Yetkisiz erişim. Lütfen izinlerinizi kontrol edin.", "minimize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "maximize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "conversationSettings": "<PERSON><PERSON><PERSON>", "maxMessages": "<PERSON><PERSON><PERSON><PERSON>", "maxMessagesDescription": "LLM'e gönderilecek maksimum mesaj sayısını sınırlar (1-1000).", "maxLoops": "Maksimum Döngü Sayısı", "maxLoopsDescription": "Sonsuz döngüleri önlemek için araç çağırma döngü sayısını sınırlar (1-1000).", "mcpServers": "MCP Sunucuları", "getApiKey": "API Anahtarı Al", "openingBrowser": "Tarayıcı açılıyor...", "confirm": "<PERSON><PERSON><PERSON>", "providerName": "Sağlayıcı Adı", "apiStyle": "API Tarzı", "enterProviderName": "Sağlayıcı adını girin", "providerNameRequired": "Sağlayıcı adı zorunludur.", "addModel": "<PERSON>", "modelName": "Model Adı", "enterModelName": "Model adın<PERSON> girin", "noApiConfigs": "Kullanılabilir API yapılandırması yok.", "add": "<PERSON><PERSON>", "fetch": "Getir", "on": "AÇIK", "off": "KAPALI", "apiUrl": "API Adresi", "selectApiStyle": "Lütfen bir API tarzı seçin", "proxySettings": "Proxy Ayarları", "enableProxy": "Proxy'y<PERSON>", "enableProxyDescription": "Etkinleş<PERSON>ril<PERSON>ğ<PERSON>e, ağ istekleri yapılandırılan proxy sunucu üzerinden gidecektir", "proxyType": "Proxy Türü", "proxyHost": "Proxy <PERSON>", "proxyPort": "Proxy Portu", "proxyUsername": "Kullanıcı Adı", "proxyPassword": "Şifre", "enterProxyHost": "Proxy sun<PERSON><PERSON> adres<PERSON> girin", "enterProxyPort": "Proxy portunu girin", "enterProxyUsername": "Kullanıcı adını girin (isteğe bağlı)", "enterProxyPassword": "<PERSON><PERSON><PERSON><PERSON> girin (isteğe bağlı)", "proxyHostRequired": "Proxy ad<PERSON><PERSON>", "proxyPortInvalid": "Proxy portu 1-65535 arasında olmalıdır", "saved": "<PERSON><PERSON><PERSON><PERSON>", "dataSync": "<PERSON><PERSON>", "syncServerRunning": "Senkronizasyon sunuc<PERSON>u <PERSON>ı<PERSON>r", "maintenance": "Bakım", "cleanupLogs": "<PERSON><PERSON>", "cleanupLogsDescription": "Günlük dosyalarını temizle", "confirmCleanup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmCleanupMessage": "Günlük dosyalarını silmek istediğinize emin misiniz? Bu işlem geri alınamaz.", "cleanupSuccess": "<PERSON>ski günlükler te<PERSON>", "cleanupFailed": "<PERSON><PERSON>zleme başarısız", "syncServerStopped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON>", "scanQRToConnect": "<PERSON><PERSON><PERSON> c<PERSON>az<PERSON> bağlanmak için bu QR kodu tarayabilir:", "addressCopied": "Adres panoya kopyalandı", "otherDevicesCanScan": "<PERSON><PERSON>er cihazlar hızlı bağlantı için bu QR kodu tarayabilir", "startServer": "<PERSON><PERSON><PERSON><PERSON>", "stopServer": "<PERSON><PERSON><PERSON><PERSON>", "connectToOtherDevices": "<PERSON><PERSON><PERSON> Cihazlara Bağlan", "scanQRCode": "QR Kod Tarayarak Bağlan", "connectionHistory": "Bağlantı Geçmişi:", "connect": "Bağlan", "manualInputAddress": "<PERSON><PERSON><PERSON> sun<PERSON>u adresini manuel o<PERSON> girin:", "serverAddress": "<PERSON><PERSON><PERSON>", "syncFromServer": "Sunucudan Senkronize Et", "pushToServer": "<PERSON><PERSON><PERSON><PERSON>", "usageInstructions": "Kullanım Talim<PERSON>ları", "desktopAsServer": "Ma<PERSON>ü<PERSON><PERSON>:", "desktopStep1": "1. \"Sunucuyu Başlat\" d<PERSON><PERSON><PERSON><PERSON> tıklayın", "desktopStep2": "2. <PERSON>bil cihazın taraması için QR kodu gösterin", "desktopStep3": "3. <PERSON><PERSON> cihaz tarama sonrası veri senkronizasyonu yapabilir", "mobileConnect": "Mobil Bağlantı:", "mobileStep1": "1. \"QR Kod Tarayarak Bağlan\" dü<PERSON><PERSON><PERSON> tıklayın", "mobileStep2": "2. Masaüstünde gösterilen QR kodu tarayın", "mobileStep3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (yükleme/indirme)", "uploadDescription": "• Yükleme: <PERSON><PERSON> c<PERSON> verileri<PERSON>", "downloadDescription": "• İndirme: <PERSON><PERSON><PERSON><PERSON> yerel cihaza veri al", "syncContent": "• Senkronizasyon İçeriği: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, MCP yapılandırmaları", "syncServerStarted": "Senkronizasyon sunuc<PERSON>u başlatıldı", "syncServerStartFailed": "<PERSON><PERSON><PERSON> ba<PERSON>ılamadı", "syncServerStopFailed": "<PERSON><PERSON><PERSON>", "scanQRCodeTitle": "QR <PERSON><PERSON>", "flashOn": "Flaş Açık", "flashOff": "Flaş <PERSON>", "aimQRCode": "QR kodu tarama çerçevesine hizalayın", "scanSyncQRCode": "Masaüstünde gösterilen senkronizasyon QR kodunu tarayın", "manualInputAddressButton": "<PERSON>", "manualInputServerAddress": "<PERSON><PERSON><PERSON>", "enterValidServerAddress": "Lütfen geçerli bir sunucu adresi girin", "scanSuccessConnectTo": "Tarama ba<PERSON>arılı, bağlanıldı: {deviceName}", "scanSuccessAddressFilled": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> adresi dolduru<PERSON>u", "scannerOpenFailed": "Tarayıcı açılamadı", "pleaseInputServerAddress": "Lütfen önce QR kodu tarayın veya sunucu adresi girin", "connectingToServer": "<PERSON><PERSON><PERSON><PERSON> bağlanıyor...", "downloadingData": "Veri indiriliyor...", "importingData": "Veri içe aktarılıyor...", "reinitializingData": "Uygulama verileri yeniden başlatılıyor...", "dataSyncSuccess": "Veri senkronizasyonu başarılı", "preparingData": "Veri hazırlanıyor...", "uploadingData": "<PERSON><PERSON>...", "dataPushSuccess": "<PERSON>eri gönderimi başarılı", "syncFailed": "Senkronizasyon başarısız", "pushFailed": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON>ar<PERSON><PERSON><PERSON><PERSON>", "justNow": "<PERSON>z önce", "minutesAgo": "{minutes} da<PERSON><PERSON>", "hoursAgo": "{hours} saat önce", "daysAgo": "{days} gün <PERSON><PERSON>", "serverSelected": "<PERSON><PERSON><PERSON>: {deviceName}", "connectionRecordDeleted": "Bağlantı kaydı silindi", "viewAllConnections": "Tüm {count} bağlantıyı gö<PERSON><PERSON>ü<PERSON>", "clearAllHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearAllConnectionHistory": "Tüm bağlantı geçmişi temizlendi", "unknownDevice": "Bilinmeyen Cihaz", "unknownPlatform": "Bilinmeyen Platform", "inmemory": "En Memoria", "toggleSidebar": "<PERSON><PERSON>", "deleteChat": "So<PERSON>bet<PERSON> Sil", "selectAll": "Tümünü Seç", "newChat": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>"}