import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/settings_provider.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Provider Disable Functionality', () {
    test('should detect when provider is disabled', () {
      // Mock provider setting
      final providerSetting = LLMProviderSetting(
        providerId: 'test-provider',
        providerName: 'Test Provider',
        apiKey: 'test-key',
        apiEndpoint: 'https://api.test.com',
        enable: false, // Disabled
      );

      // Test that disabled provider is detected
      final isEnabled = providerSetting.enable ?? true;
      expect(isEnabled, false);
    });

    test('should handle null enable value as enabled', () {
      final providerSetting = LLMProviderSetting(
        providerId: 'test-provider',
        providerName: 'Test Provider',
        apiKey: 'test-key',
        apiEndpoint: 'https://api.test.com',
        enable: null, // Should be treated as enabled
      );

      final isEnabled = providerSetting.enable ?? true;
      expect(isEnabled, true);
    });

    test('should validate provider disable logic', () {
      final testCases = [
        {'enable': true, 'expected': true},
        {'enable': false, 'expected': false},
        {'enable': null, 'expected': true},
      ];

      for (final testCase in testCases) {
        final providerSetting = LLMProviderSetting(
          providerId: 'test',
          enable: testCase['enable'] as bool?,
        );

        final isEnabled = providerSetting.enable ?? true;
        expect(isEnabled, testCase['expected'], 
               reason: 'enable=${testCase['enable']} should result in ${testCase['expected']}');
      }
    });

    test('should handle provider switching when current is disabled', () {
      // Mock available models
      final availableModels = [
        llm_model.Model(
          name: 'claude-3-sonnet',
          label: 'Claude 3 Sonnet',
          providerId: 'claude',
          icon: 'claude',
          providerName: 'Anthropic',
          apiStyle: 'claude',
        ),
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];

      // Test that we can find alternative models
      expect(availableModels.isNotEmpty, true);
      
      // Test switching logic
      final currentProviderId = 'claude';
      final alternativeModels = availableModels.where((m) => m.providerId != currentProviderId).toList();
      expect(alternativeModels.isNotEmpty, true);
      expect(alternativeModels.first.providerId, 'openai');
    });

    test('should handle error when no providers are available', () {
      final availableModels = <llm_model.Model>[];
      
      // Should handle empty list gracefully
      expect(availableModels.isEmpty, true);
      
      // Test error condition
      expect(() {
        if (availableModels.isEmpty) {
          throw Exception('No enabled AI providers available');
        }
      }, throwsException);
    });
  });
}
