import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/utils/platform.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

void main() {
  group('Platform utilities', () {
    test('Platform constants should be defined', () {
      // These tests just verify the platform utilities work
      expect(kIsDesktop, isA<bool>());
      expect(kIsMobile, isA<bool>());
    });

    test('At least one platform should be true', () {
      final platforms = [kIsWeb, kIsIOS, kIsAndroid, kIsMacOS, kIsWindows, kIsLinux];
      expect(platforms.any((platform) => platform), isTrue);
    });
  });
}
