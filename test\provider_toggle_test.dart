import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/settings_provider.dart';

void main() {
  group('Provider Toggle Logic Tests', () {
    test('LLMProviderSetting enable field logic', () {
      // Test that null means enabled
      final enabledProvider = LLMProviderSetting(
        apiKey: 'test-key',
        apiEndpoint: 'https://test.com',
        providerId: 'enabled-provider',
        enable: null, // null means enabled
      );

      final isEnabled1 = enabledProvider.enable ?? true;
      expect(isEnabled1, isTrue);

      // Test that false means disabled
      final disabledProvider = LLMProviderSetting(
        apiKey: 'test-key',
        apiEndpoint: 'https://test.com',
        providerId: 'disabled-provider',
        enable: false, // explicitly disabled
      );

      final isEnabled2 = disabledProvider.enable ?? true;
      expect(isEnabled2, isFalse);

      // Test that true means enabled
      final explicitlyEnabledProvider = LLMProviderSetting(
        apiKey: 'test-key',
        apiEndpoint: 'https://test.com',
        providerId: 'explicit-enabled-provider',
        enable: true, // explicitly enabled
      );

      final isEnabled3 = explicitlyEnabledProvider.enable ?? true;
      expect(isEnabled3, isTrue);
    });

    test('Provider filtering logic', () {
      // Create test providers - one enabled, one disabled
      final providers = [
        LLMProviderSetting(
          apiKey: 'enabled-key',
          apiEndpoint: 'https://enabled.com',
          providerId: 'enabled-provider',
          providerName: 'Enabled Provider',
          enable: null, // enabled (null means enabled)
        ),
        LLMProviderSetting(
          apiKey: 'disabled-key',
          apiEndpoint: 'https://disabled.com',
          providerId: 'disabled-provider',
          providerName: 'Disabled Provider',
          enable: false, // disabled
        ),
        LLMProviderSetting(
          apiKey: 'explicit-enabled-key',
          apiEndpoint: 'https://explicit-enabled.com',
          providerId: 'explicit-enabled-provider',
          providerName: 'Explicit Enabled Provider',
          enable: true, // explicitly enabled
        ),
      ];

      // Test filtering logic
      final enabledProviders = providers.where((setting) => setting.enable ?? true).toList();

      expect(enabledProviders.length, equals(2));
      expect(enabledProviders.map((p) => p.providerId).toList(),
             containsAll(['enabled-provider', 'explicit-enabled-provider']));
      expect(enabledProviders.any((p) => p.providerId == 'disabled-provider'), isFalse);
    });
  });
}
