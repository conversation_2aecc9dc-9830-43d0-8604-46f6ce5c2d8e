# GitHub Pages Deployment Guide

This document explains how to deploy <PERSON><PERSON> to GitHub Pages using GitHub Actions.

## Automatic Deployment

The repository is configured with GitHub Actions that will automatically:

1. **Build and test** on every push and pull request
2. **Deploy to GitHub Pages** when you push to the main/master branch
3. **<PERSON><PERSON> releases** when you create a new tag

## Setup Instructions

### 1. Enable GitHub Pages

1. Go to your repository settings on GitHub
2. Navigate to "Pages" in the left sidebar
3. Under "Source", select "GitHub Actions"
4. The site will be available at: `https://jivinsardine.github.io/Bervin-v2/`

### 2. Repository Settings

Ensure your repository has the following permissions:
- Go to Settings → Actions → General
- Under "Workflow permissions", select "Read and write permissions"
- Check "Allow GitHub Actions to create and approve pull requests"

### 3. Branch Protection (Optional but Recommended)

1. Go to Settings → Branches
2. Add a branch protection rule for `main` (or `master`)
3. Enable "Require status checks to pass before merging"
4. Select the build workflow as required

## Workflows

### 1. Deploy Workflow (`.github/workflows/deploy.yml`)
- Triggers on push to main/master branch
- Builds the Flutter web app
- Deploys to GitHub Pages
- Runs tests and analysis

### 2. Build Workflow (`.github/workflows/build.yml`)
- Triggers on push and pull requests
- Tests code formatting and analysis
- Builds for multiple platforms (Web, Android, Linux, Windows, macOS)
- Uploads build artifacts

### 3. Release Workflow (`.github/workflows/release.yml`)
- Triggers when you create a new tag (e.g., `v1.0.0`)
- Builds for all platforms
- Creates a GitHub release with downloadable binaries

## Creating a Release

To create a new release:

1. Update the version in `pubspec.yaml`
2. Commit your changes
3. Create and push a tag:
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```
4. The release workflow will automatically create a release with binaries

## Manual Deployment

If you need to deploy manually:

```bash
# Build for web with the correct base href
flutter build web --release --web-renderer html --base-href "/Bervin-v2/"

# The built files will be in build/web/
# You can upload these to any web server
```

## Environment Variables

The workflows don't require any secret environment variables for basic deployment. However, if you want to:

- Sign Android APKs: Add signing key secrets
- Deploy to custom domains: Add CNAME configuration
- Use external services: Add API keys as secrets

## Troubleshooting

### Common Issues

1. **Build fails**: Check that all dependencies are properly listed in `pubspec.yaml`
2. **Tests fail**: Ensure all tests pass locally with `flutter test`
3. **Web app doesn't load**: Verify the base href is correct for your repository name
4. **Pages not updating**: Check the Actions tab for deployment status

### Checking Deployment Status

1. Go to the "Actions" tab in your repository
2. Look for the latest "Deploy to GitHub Pages" workflow
3. Check the logs if there are any failures

## Customization

### Changing the Base Path

If you rename your repository, update the base href in the deploy workflow:
```yaml
flutter build web --release --web-renderer html --base-href "/NEW-REPO-NAME/"
```

### Adding Custom Domain

1. Add a `CNAME` file to the `web/` directory with your domain
2. Configure your domain's DNS to point to GitHub Pages
3. Update the base href to `/` in the workflow

## Security

- The workflows use official GitHub Actions
- No sensitive data is exposed in the build process
- All dependencies are pulled from official sources
