import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Gemini Model Fetch Filtering', () {
    test('should filter out deprecated Gemini models from fetch results', () {
      // Mock response from Gemini API
      final mockApiResponse = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.0-pro', // Deprecated
        'gemini-1.0-pro-vision', // Deprecated
        'gemini-1.0-pro-vision-latest', // Deprecated
        'gemini-pro', // Deprecated
        'gemini-pro-vision', // Deprecated
        'gemini-1.5-pro-latest',
      ];

      final filteredModels = _filterDeprecatedGeminiModels(mockApiResponse);

      // Should only contain non-deprecated models
      expect(filteredModels.length, 3);
      expect(filteredModels.contains('gemini-1.5-pro'), true);
      expect(filteredModels.contains('gemini-1.5-flash'), true);
      expect(filteredModels.contains('gemini-1.5-pro-latest'), true);

      // Should not contain deprecated models
      expect(filteredModels.contains('gemini-1.0-pro'), false);
      expect(filteredModels.contains('gemini-1.0-pro-vision'), false);
      expect(filteredModels.contains('gemini-1.0-pro-vision-latest'), false);
      expect(filteredModels.contains('gemini-pro'), false);
      expect(filteredModels.contains('gemini-pro-vision'), false);
    });

    test('should handle empty model list', () {
      final emptyList = <String>[];
      final filteredModels = _filterDeprecatedGeminiModels(emptyList);
      
      expect(filteredModels.isEmpty, true);
    });

    test('should handle list with only deprecated models', () {
      final deprecatedOnlyList = [
        'gemini-1.0-pro',
        'gemini-1.0-pro-vision',
        'gemini-pro',
        'gemini-pro-vision',
      ];
      
      final filteredModels = _filterDeprecatedGeminiModels(deprecatedOnlyList);
      
      expect(filteredModels.isEmpty, true);
    });

    test('should handle list with only valid models', () {
      final validOnlyList = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.5-pro-latest',
      ];
      
      final filteredModels = _filterDeprecatedGeminiModels(validOnlyList);
      
      expect(filteredModels.length, 3);
      expect(filteredModels, validOnlyList);
    });

    test('should handle case insensitive filtering', () {
      final mixedCaseList = [
        'GEMINI-1.5-PRO',
        'Gemini-1.0-Pro', // Should be filtered
        'gemini-1.5-flash',
        'GEMINI-PRO', // Should be filtered
      ];
      
      final filteredModels = _filterDeprecatedGeminiModels(mixedCaseList);
      
      expect(filteredModels.length, 2);
      expect(filteredModels.contains('GEMINI-1.5-PRO'), true);
      expect(filteredModels.contains('gemini-1.5-flash'), true);
      expect(filteredModels.contains('Gemini-1.0-Pro'), false);
      expect(filteredModels.contains('GEMINI-PRO'), false);
    });

    test('should validate deprecated model patterns', () {
      final deprecatedPatterns = [
        'gemini-1.0-pro',
        'gemini-1.0-pro-vision',
        'gemini-1.0-pro-vision-latest',
        'gemini-pro',
        'gemini-pro-vision',
      ];

      for (final pattern in deprecatedPatterns) {
        final isDeprecated = _isGeminiModelDeprecated(pattern);
        expect(isDeprecated, true, reason: 'Pattern "$pattern" should be detected as deprecated');
      }
    });

    test('should validate valid model patterns', () {
      final validPatterns = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.5-pro-latest',
        'gemini-2.0-pro', // Future model
      ];

      for (final pattern in validPatterns) {
        final isDeprecated = _isGeminiModelDeprecated(pattern);
        expect(isDeprecated, false, reason: 'Pattern "$pattern" should not be detected as deprecated');
      }
    });

    test('should log filtering statistics', () {
      final mockApiResponse = [
        'gemini-1.5-pro',
        'gemini-1.0-pro', // Deprecated
        'gemini-1.5-flash',
        'gemini-pro', // Deprecated
      ];

      final filteredModels = _filterDeprecatedGeminiModels(mockApiResponse);
      final totalModels = mockApiResponse.length;
      final validModels = filteredModels.length;
      final filteredOut = totalModels - validModels;

      expect(totalModels, 4);
      expect(validModels, 2);
      expect(filteredOut, 2);
    });

    test('should handle models with additional suffixes', () {
      final modelsWithSuffixes = [
        'gemini-1.5-pro-experimental',
        'gemini-1.0-pro-beta', // Should be filtered (contains deprecated pattern)
        'gemini-1.5-flash-preview',
        'gemini-pro-test', // Should be filtered (contains deprecated pattern)
      ];

      final filteredModels = _filterDeprecatedGeminiModels(modelsWithSuffixes);

      expect(filteredModels.length, 2);
      expect(filteredModels.contains('gemini-1.5-pro-experimental'), true);
      expect(filteredModels.contains('gemini-1.5-flash-preview'), true);
      expect(filteredModels.contains('gemini-1.0-pro-beta'), false);
      expect(filteredModels.contains('gemini-pro-test'), false);
    });
  });
}

// Test helper functions that mimic the actual filtering logic
List<String> _filterDeprecatedGeminiModels(List<String> allModels) {
  final deprecatedGeminiModels = [
    'gemini-1.0-pro',
    'gemini-1.0-pro-vision',
    'gemini-1.0-pro-vision-latest',
    'gemini-pro',
    'gemini-pro-vision',
  ];

  return allModels.where((modelName) {
    final name = modelName.toLowerCase();
    return !deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
  }).toList();
}

bool _isGeminiModelDeprecated(String modelName) {
  final name = modelName.toLowerCase();
  final deprecatedGeminiModels = [
    'gemini-1.0-pro',
    'gemini-1.0-pro-vision',
    'gemini-1.0-pro-vision-latest',
    'gemini-pro',
    'gemini-pro-vision',
  ];
  
  return deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
}
