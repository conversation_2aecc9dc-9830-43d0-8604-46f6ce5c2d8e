import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Model Selector Update Logic', () {
    test('should find current model in available models', () {
      final availableModels = [
        llm_model.Model(
          name: 'claude-3-sonnet',
          label: 'Claude 3 Sonnet',
          providerId: 'claude',
          icon: 'claude',
          providerName: 'Anthropic',
          apiStyle: 'claude',
        ),
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];

      final currentModel = llm_model.Model(
        name: 'claude-3-sonnet',
        label: 'Claude 3 Sonnet',
        providerId: 'claude',
        icon: 'claude',
        providerName: 'Anthropic',
        apiStyle: 'claude',
      );

      // Test the matching logic
      final foundModel = availableModels.firstWhere(
        (model) => model.name == currentModel.name && model.providerId == currentModel.providerId,
        orElse: () => llm_model.Model(
          name: '',
          label: 'Not found',
          providerId: '',
          icon: '',
          providerName: '',
          apiStyle: '',
        ),
      );

      expect(foundModel.name, 'claude-3-sonnet');
      expect(foundModel.providerId, 'claude');
    });

    test('should handle case when current model is not in available models', () {
      final availableModels = [
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];

      final currentModel = llm_model.Model(
        name: 'claude-3-sonnet',
        label: 'Claude 3 Sonnet',
        providerId: 'claude',
        icon: 'claude',
        providerName: 'Anthropic',
        apiStyle: 'claude',
      );

      // Test fallback logic
      final foundModel = availableModels.firstWhere(
        (model) => model.name == currentModel.name && model.providerId == currentModel.providerId,
        orElse: () {
          // If current model has valid data, use it even if not in available list
          if (currentModel.name.isNotEmpty && currentModel.providerId.isNotEmpty) {
            return currentModel;
          }
          return llm_model.Model(
            name: '',
            label: 'Loading...',
            providerId: '',
            icon: '',
            providerName: '',
            apiStyle: '',
          );
        },
      );

      // Should return the current model even if not in available list
      expect(foundModel.name, 'claude-3-sonnet');
      expect(foundModel.providerId, 'claude');
    });

    test('should show loading when no valid model is available', () {
      final availableModels = <llm_model.Model>[];

      final currentModel = llm_model.Model(
        name: '',
        label: '',
        providerId: '',
        icon: '',
        providerName: '',
        apiStyle: '',
      );

      final foundModel = availableModels.firstWhere(
        (model) => model.name == currentModel.name && model.providerId == currentModel.providerId,
        orElse: () {
          if (currentModel.name.isNotEmpty && currentModel.providerId.isNotEmpty) {
            return currentModel;
          }
          return llm_model.Model(
            name: '',
            label: availableModels.isEmpty ? 'No AI configured' : 'Loading...',
            providerId: '',
            icon: availableModels.isEmpty ? 'warning' : '',
            providerName: '',
            apiStyle: '',
          );
        },
      );

      expect(foundModel.label, 'No AI configured');
      expect(foundModel.icon, 'warning');
    });
  });
}
