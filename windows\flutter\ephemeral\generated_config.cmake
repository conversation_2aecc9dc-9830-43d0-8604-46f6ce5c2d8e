# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2" PROJECT_DIR)

set(FLUTTER_VERSION "0.0.70" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 70 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Flutter\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2"
  "FLUTTER_ROOT=C:\\Flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\OneDrive\\Desktop\\Bervin-v2\\.dart_tool\\package_config.json"
)
