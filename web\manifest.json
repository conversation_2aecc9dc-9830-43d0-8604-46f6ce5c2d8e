{"name": "<PERSON><PERSON>", "short_name": "<PERSON><PERSON>", "start_url": ".", "display": "standalone", "background_color": "#hexcode", "theme_color": "#hexcode", "description": "<PERSON><PERSON> <PERSON> <PERSON> with MCP", "orientation": "portrait-primary", "prefer_related_applications": false, "icons": [{"src": "icons/Icon-192.png", "sizes": "192x192", "type": "image/png"}, {"src": "icons/Icon-512.png", "sizes": "512x512", "type": "image/png"}, {"src": "icons/Icon-maskable-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "icons/Icon-maskable-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}]}