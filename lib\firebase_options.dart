// firebase_options.dart
// File generated by FlutterFire CLI.

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: "AIzaSyBGiiHf4zGqrlh5GlMMJi0lp4uVWYaI_3o",
    authDomain: "bervin-56472.firebaseapp.com",
    projectId: "bervin-56472",
    storageBucket: "bervin-56472.firebasestorage.app",
    messagingSenderId: "463259268243",
    appId: "1:463259268243:web:e8a6a669598466be6c8966"
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: "AIzaSyBGiiHf4zGqrlh5GlMMJi0lp4uVWYaI_3o",
    appId: "1:463259268243:web:e8a6a669598466be6c8966",
    messagingSenderId: '463259268243',
    projectId: 'bervin-56472',
    storageBucket: 'bervin-56472.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: "AIzaSyBGiiHf4zGqrlh5GlMMJi0lp4uVWYaI_3o",
    appId: "1:463259268243:web:e8a6a669598466be6c8966",
    messagingSenderId: '463259268243',
    projectId: 'bervin-56472',
    storageBucket: 'bervin-56472.firebasestorage.app',
    iosBundleId: 'com.hubcrea.Bervin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: "AIzaSyBGiiHf4zGqrlh5GlMMJi0lp4uVWYaI_3o",
    appId: "1:463259268243:web:e8a6a669598466be6c8966",
    messagingSenderId: '463259268243',
    projectId: 'bervin-56472',
    storageBucket: 'bervin-56472.firebasestorage.app',
    iosBundleId: 'com.hubcrea.Bervin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: "AIzaSyBGiiHf4zGqrlh5GlMMJi0lp4uVWYaI_3o",
    appId: "1:463259268243:web:e8a6a669598466be6c8966",
    messagingSenderId: '463259268243',
    projectId: 'bervin-56472',
    storageBucket: 'bervin-56472.firebasestorage.app',
  );
}
