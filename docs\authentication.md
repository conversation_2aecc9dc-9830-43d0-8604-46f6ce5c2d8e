# Firebase Authentication Integration

This document describes the Google authentication integration using Firebase Auth for the Bervin Flutter application.

## Overview

The app now includes:
- Google Sign-In using Firebase Authentication
- User session management
- Account management and deletion
- Authentication state management using Provider pattern

## Files Added/Modified

### New Files:
- `lib/page/auth/auth_wrapper.dart` - Determines which screen to show based on auth state
- `lib/page/auth/login_screen.dart` - Login screen with Google sign-in
- `lib/page/auth/user_profile.dart` - User profile widget in sidebar
- `lib/page/auth/account_screen.dart` - Account management screen
- `lib/provider/auth_provider.dart` - Authentication state management
- `lib/firebase_options.dart` - Firebase configuration (already existed)

### Modified Files:
- `lib/main.dart` - Added Firebase initialization and AuthWrapper
- `lib/provider/provider_manager.dart` - Added AuthProvider
- `lib/page/layout/sidebar.dart` - Added UserProfile widget
- `lib/services/auth/auth_service.dart` - Already existed
- `pubspec.yaml` - Firebase dependencies already added

## Features

### 1. Google Sign-In
- Users can sign in with their Google account
- Handled through Firebase Authentication
- Automatic session management

### 2. User Profile
- Displays user information in the sidebar
- Shows user avatar, name, and email
- Dropdown with account options

### 3. Account Management
- View account information
- Delete account functionality
- Sign out option

### 4. Authentication Flow
- App starts with AuthWrapper checking auth state
- Unauthenticated users see login screen
- Authenticated users see main app
- Real-time auth state updates

## Configuration Required

### Firebase Project Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication and configure Google sign-in
3. Add your app to the Firebase project (Web, iOS, Android)
4. Download and replace configuration files:
   - `lib/firebase_options.dart` (update with your project details)
   - `android/app/google-services.json` (for Android)
   - `ios/Runner/GoogleService-Info.plist` (for iOS)

### Google Sign-In Configuration
1. Configure OAuth consent screen in Google Cloud Console
2. Add authorized domains for web deployment
3. Configure iOS URL schemes (for iOS)
4. Add SHA-1 fingerprints (for Android)

## Current Configuration State

The app currently uses placeholder configuration in `firebase_options.dart`. To use authentication:

1. Replace placeholder values with actual Firebase project configuration
2. Configure Google Sign-In in Firebase Console
3. Add appropriate platform-specific configuration files

## Authentication Provider

The `AuthProvider` class manages authentication state:
- `user` - Current authenticated user
- `isLoading` - Loading state during auth operations
- `errorMessage` - Error messages from auth operations
- `signInWithGoogle()` - Google sign-in method
- `signOut()` - Sign out method
- `deleteAccount()` - Account deletion method

## UI Components

### LoginScreen
- Clean, modern design
- Google sign-in button
- Feature highlights
- Loading states and error handling

### UserProfile
- Compact user info display
- Dropdown with account options
- Sign out functionality

### AccountScreen
- Detailed user information
- Account management options
- Danger zone for account deletion

## Usage

The authentication is automatically integrated into the app flow:
1. App launches and checks authentication state
2. Unauthenticated users see login screen
3. Users can sign in with Google
4. Authenticated users access main app
5. User profile accessible from sidebar
6. Account management through profile dropdown

## Testing

To test the authentication:
1. Configure Firebase project with valid credentials
2. Run the app: `flutter run`
3. Try signing in with Google account
4. Test user profile and account management features
5. Test sign out and re-authentication

## Security Notes

- Never commit real Firebase configuration to version control
- Use environment variables or secure configuration management
- Regularly rotate API keys and tokens
- Monitor authentication usage in Firebase Console
- Implement proper error handling for production use

## Future Enhancements

Potential improvements:
- Additional sign-in providers (Apple, Microsoft, etc.)
- Two-factor authentication
- Profile editing capabilities
- Data synchronization between devices
- Offline authentication handling
