import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Quota URL Redirection', () {
    test('should return correct quota URLs for each provider', () {
      final testCases = [
        {
          'provider': 'gemini',
          'expectedName': 'Gemini',
          'expectedUrl': 'https://aistudio.google.com/app/apikey',
        },
        {
          'provider': 'openai',
          'expectedName': 'OpenAI',
          'expectedUrl': 'https://platform.openai.com/usage',
        },
        {
          'provider': 'claude',
          'expectedName': 'Claude',
          'expectedUrl': 'https://console.anthropic.com/settings/billing',
        },
        {
          'provider': 'deepseek',
          'expectedName': 'DeepSeek',
          'expectedUrl': 'https://platform.deepseek.com/usage',
        },
        {
          'provider': '302.ai',
          'expectedName': '302.AI',
          'expectedUrl': 'https://302.ai/account/billing',
        },
        {
          'provider': 'openrouter',
          'expectedName': 'OpenRouter',
          'expectedUrl': 'https://openrouter.ai/credits',
        },
      ];

      for (final testCase in testCases) {
        final provider = testCase['provider'] as String;
        final expectedName = testCase['expectedName'] as String;
        final expectedUrl = testCase['expectedUrl'] as String;

        final providerInfo = _getProviderQuotaInfo(provider);

        expect(providerInfo['name'], expectedName, reason: 'Provider name for $provider');
        expect(providerInfo['url'], expectedUrl, reason: 'Provider URL for $provider');
      }
    });

    test('should handle unknown providers with fallback', () {
      final unknownProvider = 'unknown-provider';
      final providerInfo = _getProviderQuotaInfo(unknownProvider);

      expect(providerInfo['name'], 'AI Provider');
      expect(providerInfo['url'], 'https://google.com/search?q=unknown-provider+api+quota+billing');
    });

    test('should handle case insensitive provider names', () {
      final testCases = [
        'GEMINI',
        'OpenAI',
        'Claude',
        'DEEPSEEK',
      ];

      for (final provider in testCases) {
        final providerInfo = _getProviderQuotaInfo(provider);
        expect(providerInfo['name'], isNotEmpty);
        expect(providerInfo['url'], isNotEmpty);
        expect(providerInfo['url'], startsWith('https://'));
      }
    });

    test('should detect quota errors from different providers', () {
      final testCases = [
        {
          'error': 'Gemini API quota exceeded',
          'expectedProvider': 'gemini',
        },
        {
          'error': 'OpenAI rate limit exceeded',
          'expectedProvider': 'openai',
        },
        {
          'error': 'Claude billing issue',
          'expectedProvider': 'claude',
        },
        {
          'error': 'DeepSeek quota exhausted',
          'expectedProvider': 'deepseek',
        },
        {
          'error': 'Google AI quota exceeded',
          'expectedProvider': 'gemini',
        },
        {
          'error': 'GPT-4 rate limit',
          'expectedProvider': 'openai',
        },
        {
          'error': 'Anthropic billing error',
          'expectedProvider': 'claude',
        },
      ];

      for (final testCase in testCases) {
        final error = testCase['error'] as String;
        final expectedProvider = testCase['expectedProvider'] as String;

        final detectedProvider = _getQuotaErrorProvider(error);
        expect(detectedProvider, expectedProvider, reason: 'Error: $error');
      }
    });

    test('should detect quota exceeded errors correctly', () {
      final quotaErrors = [
        'API quota exceeded',
        'Rate limit exceeded',
        'Resource exhausted',
        'Billing account suspended',
        'Insufficient quota',
        'Usage limit reached',
        'Credit balance low',
        'Payment required',
      ];

      for (final error in quotaErrors) {
        final isQuotaError = _isQuotaExceededError(error);
        expect(isQuotaError, true, reason: 'Should detect: $error');
      }
    });

    test('should not detect non-quota errors', () {
      final nonQuotaErrors = [
        'Network connection failed',
        'Invalid API key',
        'Model not found',
        'Request timeout',
        'Server error',
        'Authentication failed',
      ];

      for (final error in nonQuotaErrors) {
        final isQuotaError = _isQuotaExceededError(error);
        expect(isQuotaError, false, reason: 'Should not detect: $error');
      }
    });

    test('should validate all quota URLs are valid', () {
      final providers = ['gemini', 'openai', 'claude', 'deepseek', '302.ai', 'openrouter'];

      for (final provider in providers) {
        final providerInfo = _getProviderQuotaInfo(provider);
        final url = providerInfo['url']!;

        expect(url, startsWith('https://'), reason: 'URL should use HTTPS: $url');
        expect(Uri.tryParse(url), isNotNull, reason: 'URL should be valid: $url');
      }
    });

    test('should handle provider fallback correctly', () {
      // Test with mock current model
      const mockCurrentModel = 'test-provider';
      final provider = _getQuotaErrorProviderWithFallback('unknown error', mockCurrentModel);
      
      expect(provider, mockCurrentModel);
    });
  });
}

// Test helper functions that mimic the actual implementation

Map<String, String> _getProviderQuotaInfo(String providerId) {
  switch (providerId.toLowerCase()) {
    case 'gemini':
      return {
        'name': 'Gemini',
        'url': 'https://aistudio.google.com/app/apikey',
      };
    case 'openai':
      return {
        'name': 'OpenAI',
        'url': 'https://platform.openai.com/usage',
      };
    case 'claude':
      return {
        'name': 'Claude',
        'url': 'https://console.anthropic.com/settings/billing',
      };
    case 'deepseek':
      return {
        'name': 'DeepSeek',
        'url': 'https://platform.deepseek.com/usage',
      };
    case '302.ai':
      return {
        'name': '302.AI',
        'url': 'https://302.ai/account/billing',
      };
    case 'openrouter':
      return {
        'name': 'OpenRouter',
        'url': 'https://openrouter.ai/credits',
      };
    default:
      return {
        'name': 'AI Provider',
        'url': 'https://google.com/search?q=$providerId+api+quota+billing',
      };
  }
}

String _getQuotaErrorProvider(String error) {
  final errorStr = error.toLowerCase();
  
  if (errorStr.contains('gemini') || errorStr.contains('google')) {
    return 'gemini';
  } else if (errorStr.contains('openai') || errorStr.contains('gpt')) {
    return 'openai';
  } else if (errorStr.contains('claude') || errorStr.contains('anthropic')) {
    return 'claude';
  } else if (errorStr.contains('deepseek')) {
    return 'deepseek';
  }
  
  return 'unknown';
}

String _getQuotaErrorProviderWithFallback(String error, String fallbackProvider) {
  final detected = _getQuotaErrorProvider(error);
  return detected != 'unknown' ? detected : fallbackProvider;
}

bool _isQuotaExceededError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('quota') ||
         errorStr.contains('resource_exhausted') ||
         errorStr.contains('rate limit') ||
         errorStr.contains('billing') ||
         errorStr.contains('exceeded your current quota') ||
         errorStr.contains('insufficient_quota') ||
         errorStr.contains('usage limit') ||
         errorStr.contains('credit') ||
         errorStr.contains('payment');
}
