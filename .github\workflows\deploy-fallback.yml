name: Deploy to GitHub Pages (Fallback)

on:
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment with master channel'
        required: false
        default: 'false'

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages-fallback"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter (Master Channel)
      uses: subosito/flutter-action@v2
      with:
        flutter-version: 'latest'
        channel: 'master'
        cache: true

    - name: Verify Dart SDK version
      run: |
        dart --version
        flutter --version
        echo "Required Dart SDK: ^3.5.4"

    - name: Install dependencies
      run: flutter pub get

    - name: Analyze code
      run: flutter analyze

    - name: Build web app (skip tests if needed)
      run: |
        flutter build web --release --web-renderer html --base-href "/Bervin-v2/"

    - name: Setup Pages
      uses: actions/configure-pages@v4

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: 'build/web'

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    
    steps:
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
