import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logging/logging.dart';

class AuthService {
  static final _logger = Logger('AuthService');
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Get current user
  static User? get currentUser => _auth.currentUser;

  // Auth state stream
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with Google
  static Future<UserCredential?> signInWithGoogle() async {
    try {
      _logger.info('Starting Google Sign In...');

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        _logger.info('Google Sign In cancelled by user');
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google user credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);

      _logger.info('Successfully signed in: ${userCredential.user?.email}');
      return userCredential;
    } catch (e) {
      _logger.severe('Error signing in with Google: $e');
      rethrow;
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      _logger.info('Signing out...');
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
      _logger.info('Successfully signed out');
    } catch (e) {
      _logger.severe('Error signing out: $e');
      rethrow;
    }
  }

  // Delete account
  static Future<void> deleteAccount() async {
    try {
      final user = _auth.currentUser;
      if (user != null) {
        _logger.info('Deleting account for: ${user.email}');
        await user.delete();
        await _googleSignIn.signOut();
        _logger.info('Account deleted successfully');
      }
    } catch (e) {
      _logger.severe('Error deleting account: $e');
      rethrow;
    }
  }

  // Get user display name
  static String? get userDisplayName => currentUser?.displayName;

  // Get user email
  static String? get userEmail => currentUser?.email;

  // Get user photo URL
  static String? get userPhotoURL => currentUser?.photoURL;

  // Check if user is signed in
  static bool get isSignedIn => currentUser != null;
}
