import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Model Fetch Filtering - Comprehensive', () {
    test('should filter deprecated Gemini models from Gemini API', () {
      // Mock Gemini API response
      final geminiApiResponse = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.0-pro', // Deprecated
        'gemini-1.0-pro-vision', // Deprecated
        'gemini-1.0-pro-vision-latest', // Deprecated
        'gemini-pro', // Deprecated
        'gemini-pro-vision', // Deprecated
        'gemini-1.5-pro-latest',
      ];

      final filteredModels = _filterGeminiModels(geminiApiResponse);

      expect(filteredModels.length, 3);
      expect(filteredModels, containsAll(['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-1.5-pro-latest']));
      expect(filteredModels, isNot(contains('gemini-1.0-pro')));
      expect(filteredModels, isNot(contains('gemini-pro')));
    });

    test('should filter deprecated Gemini models from OpenAI-compatible APIs', () {
      // Mock 302.AI or OpenRouter response that includes Gemini models
      final openaiCompatibleResponse = [
        'gpt-4',
        'gpt-3.5-turbo',
        'claude-3-sonnet',
        'gemini-1.5-pro',
        'gemini-1.0-pro', // Deprecated - should be filtered
        'gemini-pro', // Deprecated - should be filtered
        'llama-2-70b',
      ];

      final filteredModels = _filterOpenAICompatibleModels(openaiCompatibleResponse);

      expect(filteredModels.length, 5);
      expect(filteredModels, contains('gpt-4'));
      expect(filteredModels, contains('claude-3-sonnet'));
      expect(filteredModels, contains('gemini-1.5-pro'));
      expect(filteredModels, contains('llama-2-70b'));
      expect(filteredModels, isNot(contains('gemini-1.0-pro')));
      expect(filteredModels, isNot(contains('gemini-pro')));
    });

    test('should not filter non-Gemini models', () {
      final mixedModels = [
        'gpt-4',
        'claude-3-sonnet',
        'llama-2-70b',
        'deepseek-chat',
        'gemini-1.0-pro', // Only this should be filtered
      ];

      final filteredModels = _filterOpenAICompatibleModels(mixedModels);

      expect(filteredModels.length, 4);
      expect(filteredModels, contains('gpt-4'));
      expect(filteredModels, contains('claude-3-sonnet'));
      expect(filteredModels, contains('llama-2-70b'));
      expect(filteredModels, contains('deepseek-chat'));
      expect(filteredModels, isNot(contains('gemini-1.0-pro')));
    });

    test('should handle empty model lists', () {
      expect(_filterGeminiModels([]), isEmpty);
      expect(_filterOpenAICompatibleModels([]), isEmpty);
    });

    test('should handle lists with only deprecated models', () {
      final onlyDeprecated = [
        'gemini-1.0-pro',
        'gemini-pro',
        'gemini-pro-vision',
      ];

      expect(_filterGeminiModels(onlyDeprecated), isEmpty);
      expect(_filterOpenAICompatibleModels(onlyDeprecated), isEmpty);
    });

    test('should handle lists with only valid models', () {
      final onlyValid = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gpt-4',
        'claude-3-sonnet',
      ];

      expect(_filterGeminiModels(['gemini-1.5-pro', 'gemini-1.5-flash']), hasLength(2));
      expect(_filterOpenAICompatibleModels(onlyValid), hasLength(4));
    });

    test('should validate deprecated model patterns', () {
      final deprecatedPatterns = [
        'gemini-1.0-pro',
        'gemini-1.0-pro-vision',
        'gemini-1.0-pro-vision-latest',
        'gemini-pro',
        'gemini-pro-vision',
      ];

      for (final pattern in deprecatedPatterns) {
        expect(_isGeminiModelDeprecated(pattern), true, reason: '$pattern should be deprecated');
      }
    });

    test('should validate valid model patterns', () {
      final validPatterns = [
        'gemini-1.5-pro',
        'gemini-1.5-flash',
        'gemini-1.5-pro-latest',
        'gemini-2.0-pro', // Future model
        'gpt-4',
        'claude-3-sonnet',
      ];

      for (final pattern in validPatterns) {
        expect(_isGeminiModelDeprecated(pattern), false, reason: '$pattern should not be deprecated');
      }
    });

    test('should handle case variations', () {
      final caseVariations = [
        'GEMINI-1.0-PRO',
        'Gemini-Pro',
        'gemini-PRO-vision',
        'GEMINI-1.5-PRO', // Valid
      ];

      expect(_isGeminiModelDeprecated('GEMINI-1.0-PRO'), true);
      expect(_isGeminiModelDeprecated('Gemini-Pro'), true);
      expect(_isGeminiModelDeprecated('gemini-PRO-vision'), true);
      expect(_isGeminiModelDeprecated('GEMINI-1.5-PRO'), false);
    });

    test('should handle models with additional suffixes', () {
      final modelsWithSuffixes = [
        'gemini-1.5-pro-experimental', // Valid
        'gemini-1.0-pro-beta', // Deprecated (contains gemini-1.0-pro)
        'gemini-pro-test', // Deprecated (contains gemini-pro)
        'gemini-1.5-flash-preview', // Valid
      ];

      final filtered = _filterOpenAICompatibleModels(modelsWithSuffixes);
      expect(filtered, hasLength(2));
      expect(filtered, contains('gemini-1.5-pro-experimental'));
      expect(filtered, contains('gemini-1.5-flash-preview'));
    });

    test('should log filtering statistics correctly', () {
      final testModels = [
        'gpt-4',
        'gemini-1.5-pro',
        'gemini-1.0-pro', // Deprecated
        'claude-3-sonnet',
        'gemini-pro', // Deprecated
      ];

      final filtered = _filterOpenAICompatibleModels(testModels);
      final totalModels = testModels.length;
      final validModels = filtered.length;
      final filteredOut = totalModels - validModels;

      expect(totalModels, 5);
      expect(validModels, 3);
      expect(filteredOut, 2);
    });
  });
}

// Test helper functions that mimic the actual filtering logic

/// Filter for Gemini API (only Gemini models)
List<String> _filterGeminiModels(List<String> allModels) {
  final deprecatedGeminiModels = [
    'gemini-1.0-pro',
    'gemini-1.0-pro-vision',
    'gemini-1.0-pro-vision-latest',
    'gemini-pro',
    'gemini-pro-vision',
  ];

  return allModels.where((modelName) {
    final name = modelName.toLowerCase();
    return !deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
  }).toList();
}

/// Filter for OpenAI-compatible APIs (mixed models, only filter Gemini ones)
List<String> _filterOpenAICompatibleModels(List<String> allModels) {
  final deprecatedGeminiModels = [
    'gemini-1.0-pro',
    'gemini-1.0-pro-vision',
    'gemini-1.0-pro-vision-latest',
    'gemini-pro',
    'gemini-pro-vision',
  ];

  return allModels.where((modelName) {
    final name = modelName.toLowerCase();
    // Only filter if it's a Gemini model
    if (!name.startsWith('gemini-')) {
      return true; // Keep non-Gemini models
    }
    // For Gemini models, filter out deprecated ones
    return !deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
  }).toList();
}

/// Check if a specific model is deprecated
bool _isGeminiModelDeprecated(String modelName) {
  final name = modelName.toLowerCase();
  final deprecatedGeminiModels = [
    'gemini-1.0-pro',
    'gemini-1.0-pro-vision',
    'gemini-1.0-pro-vision-latest',
    'gemini-pro',
    'gemini-pro-vision',
  ];
  
  return name.startsWith('gemini-') && 
         deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
}
