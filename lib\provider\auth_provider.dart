import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:bervin/services/auth/auth_service.dart';
import 'package:logging/logging.dart';

class AuthProvider extends ChangeNotifier {
  static final _logger = Logger('AuthProvider');

  User? _user;
  bool _isLoading = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _user != null;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    AuthService.authStateChanges.listen((User? user) {
      _user = user;
      _logger.info('Auth state changed: ${user?.email ?? 'Not signed in'}');
      notifyListeners();
    });

    // Get current user
    _user = AuthService.currentUser;
  }

  Future<bool> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await AuthService.signInWithGoogle();
      if (result != null) {
        _logger.info('Successfully signed in: ${result.user?.email}');
        return true;
      }
      return false;
    } catch (e) {
      _logger.severe('Failed to sign in with Google: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.signOut();
      _logger.info('User signed out successfully');
      return true;
    } catch (e) {
      _logger.severe('Failed to sign out: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteAccount() async {
    _setLoading(true);
    _clearError();

    try {
      await AuthService.deleteAccount();
      _logger.info('Account deleted successfully');
      return true;
    } catch (e) {
      _logger.severe('Failed to delete account: $e');
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
