import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Model Selector Validation', () {
    test('should not show invalid models in UI', () {
      // Mock available models (empty - simulating disabled providers)
      final availableModels = <llm_model.Model>[];
      
      // Mock current model (invalid - from disabled provider)
      final currentModel = llm_model.Model(
        name: 'gemini-1.5-pro-latest',
        label: 'Gemini 1.5 Pro Latest',
        providerId: 'gemini',
        icon: 'gemini',
        providerName: 'Google',
        apiStyle: 'gemini',
      );

      // Test the model selector logic
      bool isCurrentModel(llm_model.Model model) {
        return model.name == currentModel.name && model.providerId == currentModel.providerId;
      }

      final displayModel = availableModels.firstWhere(
        (model) => isCurrentModel(model),
        orElse: () {
          // This is the fixed logic - should return placeholder, not invalid model
          return llm_model.Model(
            name: '',
            label: availableModels.isEmpty ? 'No AI configured' : 'Select AI model',
            providerId: '',
            icon: availableModels.isEmpty ? 'warning' : 'info',
            providerName: '',
            apiStyle: '',
          );
        },
      );

      // Should show placeholder, not the invalid Gemini model
      expect(displayModel.name, '');
      expect(displayModel.label, 'No AI configured');
      expect(displayModel.icon, 'warning');
    });

    test('should show valid model when available', () {
      // Mock available models with valid options
      final availableModels = [
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];
      
      // Mock current model (valid)
      final currentModel = llm_model.Model(
        name: 'gpt-4',
        label: 'GPT-4',
        providerId: 'openai',
        icon: 'openai',
        providerName: 'OpenAI',
        apiStyle: 'openai',
      );

      bool isCurrentModel(llm_model.Model model) {
        return model.name == currentModel.name && model.providerId == currentModel.providerId;
      }

      final displayModel = availableModels.firstWhere(
        (model) => isCurrentModel(model),
        orElse: () {
          return llm_model.Model(
            name: '',
            label: availableModels.isEmpty ? 'No AI configured' : 'Select AI model',
            providerId: '',
            icon: availableModels.isEmpty ? 'warning' : 'info',
            providerName: '',
            apiStyle: '',
          );
        },
      );

      // Should show the valid model
      expect(displayModel.name, 'gpt-4');
      expect(displayModel.providerId, 'openai');
    });

    test('should handle provider disable validation', () {
      // Mock provider settings
      final providers = [
        {'providerId': 'gemini', 'enable': false},
        {'providerId': 'openai', 'enable': true},
      ];

      // Test getEnabledProviderSetting logic
      Map<String, dynamic>? getEnabledProviderSetting(String providerId) {
        try {
          final setting = providers.firstWhere((element) => element['providerId'] == providerId);
          final isEnabled = setting['enable'] ?? true;
          return isEnabled ? setting : null;
        } catch (e) {
          return null;
        }
      }

      expect(getEnabledProviderSetting('gemini'), isNull);
      expect(getEnabledProviderSetting('openai'), isNotNull);
      expect(getEnabledProviderSetting('nonexistent'), isNull);
    });

    test('should validate deprecated model detection', () {
      final testCases = [
        {'name': 'gemini-1.5-pro-latest', 'providerId': 'gemini', 'expected': false},
        {'name': 'gemini-1.0-pro-vision-latest', 'providerId': 'gemini', 'expected': true},
        {'name': 'gpt-4', 'providerId': 'openai', 'expected': false},
      ];

      for (final testCase in testCases) {
        final isDeprecated = _isModelDeprecated(testCase['name'] as String, testCase['providerId'] as String);
        expect(isDeprecated, testCase['expected'], 
               reason: 'Model ${testCase['name']} should ${testCase['expected'] ? 'be' : 'not be'} deprecated');
      }
    });
  });
}

// Test helper function
bool _isModelDeprecated(String modelName, String providerId) {
  final name = modelName.toLowerCase();
  
  if (providerId == 'gemini') {
    final deprecatedGeminiModels = [
      'gemini-1.0-pro',
      'gemini-1.0-pro-vision',
      'gemini-1.0-pro-vision-latest',
      'gemini-pro',
      'gemini-pro-vision',
    ];
    
    return deprecatedGeminiModels.any((deprecated) => name.contains(deprecated));
  }
  
  return false;
}
