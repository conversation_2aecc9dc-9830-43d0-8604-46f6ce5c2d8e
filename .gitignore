# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/flutter_export_environment.sh
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Coverage
coverage/

# Test related
test_driver/
integration_test/

# Environment variables
.env
.env.local
.env.production.local
.env.test.local

# Signing files
*.p12
*.jks
*.keystore
key.properties

# Temporary files
*.tmp
*.temp

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Web
/web/favicon.png.old
/web/manifest.json.old

# Platform specific
.vscode/settings.json

# GitHub Actions artifacts
artifacts/
