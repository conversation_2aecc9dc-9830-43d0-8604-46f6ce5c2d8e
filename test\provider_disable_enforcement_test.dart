import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/settings_provider.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Provider Disable Enforcement', () {
    test('should exclude disabled providers from available models', () {
      // Mock provider settings
      final providerSettings = [
        LLMProviderSetting(
          providerId: 'gemini',
          providerName: 'Google Gemini',
          apiKey: 'test-key',
          apiEndpoint: 'https://api.gemini.com',
          enable: false, // Disabled
          enabledModels: ['gemini-1.5-pro-latest'],
          icon: 'gemini',
        ),
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'test-key',
          apiEndpoint: 'https://api.openai.com',
          enable: true, // Enabled
          enabledModels: ['gpt-4'],
          icon: 'openai',
        ),
      ];

      // Test the filtering logic
      final enabledSettings = providerSettings.where((setting) => setting.enable ?? true).toList();
      
      expect(enabledSettings.length, 1);
      expect(enabledSettings.first.providerId, 'openai');
    });

    test('should detect when provider is disabled', () {
      final disabledProvider = LLMProviderSetting(
        providerId: 'gemini',
        enable: false,
      );

      final enabledProvider = LLMProviderSetting(
        providerId: 'openai',
        enable: true,
      );

      final nullProvider = LLMProviderSetting(
        providerId: 'claude',
        enable: null, // Should be treated as enabled
      );

      expect(disabledProvider.enable ?? true, false);
      expect(enabledProvider.enable ?? true, true);
      expect(nullProvider.enable ?? true, true);
    });

    test('should handle provider switching logic', () {
      final availableModels = [
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
        llm_model.Model(
          name: 'claude-3-sonnet',
          label: 'Claude 3 Sonnet',
          providerId: 'claude',
          icon: 'claude',
          providerName: 'Anthropic',
          apiStyle: 'claude',
        ),
      ];

      final disabledProviderId = 'gemini';
      
      // Test switching away from disabled provider
      final alternativeModels = availableModels
          .where((model) => model.providerId != disabledProviderId)
          .toList();

      expect(alternativeModels.length, 2);
      expect(alternativeModels.any((m) => m.providerId == 'gemini'), false);
      expect(alternativeModels.any((m) => m.providerId == 'openai'), true);
      expect(alternativeModels.any((m) => m.providerId == 'claude'), true);
    });

    test('should handle case when no alternative providers exist', () {
      final availableModels = [
        llm_model.Model(
          name: 'gemini-1.5-pro-latest',
          label: 'Gemini 1.5 Pro Latest',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
      ];

      final disabledProviderId = 'gemini';
      
      // Test when all models are from disabled provider
      final alternativeModels = availableModels
          .where((model) => model.providerId != disabledProviderId)
          .toList();

      expect(alternativeModels.isEmpty, true);
    });

    test('should validate getEnabledProviderSetting logic', () {
      final settings = [
        LLMProviderSetting(providerId: 'enabled', enable: true),
        LLMProviderSetting(providerId: 'disabled', enable: false),
        LLMProviderSetting(providerId: 'null-enabled', enable: null),
      ];

      // Mock the getEnabledProviderSetting logic
      LLMProviderSetting? getEnabledProviderSetting(String providerId) {
        try {
          final setting = settings.firstWhere((element) => element.providerId == providerId);
          final isEnabled = setting.enable ?? true;
          return isEnabled ? setting : null;
        } catch (e) {
          return null;
        }
      }

      expect(getEnabledProviderSetting('enabled'), isNotNull);
      expect(getEnabledProviderSetting('disabled'), isNull);
      expect(getEnabledProviderSetting('null-enabled'), isNotNull);
      expect(getEnabledProviderSetting('nonexistent'), isNull);
    });
  });
}
