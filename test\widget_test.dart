import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/main.dart';

void main() {
  testWidgets('App starts without crashing', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the app starts without throwing any exceptions
    // Look for any widget that confirms the app loaded
    await tester.pumpAndSettle();

    // The test passes if no exceptions are thrown during widget creation
    expect(true, isTrue); // Simple assertion to confirm test completed
  });

  group('Basic app tests', () {
    test('App should have correct name', () {
      expect('Be<PERSON>', equals('<PERSON><PERSON>'));
    });

    test('Package name should be lowercase', () {
      const packageName = 'bervin';
      expect(packageName, equals(packageName.toLowerCase()));
    });
  });
}
