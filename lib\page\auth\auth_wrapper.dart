import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:bervin/provider/auth_provider.dart';
import 'package:bervin/page/auth/login_screen.dart';
import 'package:bervin/page/layout/layout.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check if user is signed in
        if (authProvider.isAuthenticated) {
          // User is signed in, show main app
          return const LayoutPage();
        } else {
          // User is not signed in, show login screen
          return const LoginScreen();
        }
      },
    );
  }
}
