import 'package:flutter_test/flutter_test.dart';

void main() {
  group('No AI Configured Error Detection', () {
    test('should detect no AI configured errors', () {
      final testCases = [
        'No enabled AI providers available. Please enable at least one provider in settings.',
        'No AI configured',
        'No valid models available',
        'Provider is disabled',
        'No provider available',
        'Exception: No enabled AI providers available',
      ];

      for (final errorMessage in testCases) {
        final isNoAIError = _isNoAIConfiguredError(errorMessage);
        expect(isNoAIError, true, reason: 'Should detect "$errorMessage" as No AI error');
      }
    });

    test('should not detect other errors as no AI configured', () {
      final testCases = [
        'Network error occurred',
        'API key is invalid',
        'Rate limit exceeded',
        'Model deprecated',
        'Unknown error occurred',
        'Connection timeout',
      ];

      for (final errorMessage in testCases) {
        final isNoAIError = _isNoAIConfiguredError(errorMessage);
        expect(isNoAIError, false, reason: 'Should not detect "$errorMessage" as No AI error');
      }
    });

    test('should handle case insensitive detection', () {
      final testCases = [
        'NO ENABLED AI PROVIDERS AVAILABLE',
        'No AI Configured',
        'NO VALID MODELS',
        'Provider Is Disabled',
      ];

      for (final errorMessage in testCases) {
        final isNoAIError = _isNoAIConfiguredError(errorMessage);
        expect(isNoAIError, true, reason: 'Should detect "$errorMessage" (case insensitive) as No AI error');
      }
    });

    test('should handle exception objects', () {
      final exception = Exception('No enabled AI providers available. Please enable at least one provider in settings.');
      final isNoAIError = _isNoAIConfiguredError(exception);
      expect(isNoAIError, true, reason: 'Should detect Exception object as No AI error');
    });

    test('should validate error message content for user dialog', () {
      // Test the user-friendly message content
      const expectedTitle = 'No AI Configured';
      const expectedMessage = 'You need to configure at least one AI provider to start chatting.';
      const expectedSubMessage = 'Go to Settings → Providers and enable an AI provider like OpenAI, Claude, or Gemini.';
      const expectedButtonText = 'Open Settings';

      // These are the messages that should be shown to users
      expect(expectedTitle.isNotEmpty, true);
      expect(expectedMessage.isNotEmpty, true);
      expect(expectedSubMessage.contains('Settings'), true);
      expect(expectedSubMessage.contains('Providers'), true);
      expect(expectedButtonText, 'Open Settings');
    });

    test('should validate error detection patterns', () {
      // Test specific patterns that should be detected
      final patterns = [
        'no enabled ai providers',
        'no ai configured',
        'no valid models',
        'provider is disabled',
        'no provider',
      ];

      for (final pattern in patterns) {
        // Test with different variations
        final variations = [
          pattern,
          pattern.toUpperCase(),
          'Error: $pattern',
          'Exception: $pattern occurred',
          'Failed: $pattern detected',
        ];

        for (final variation in variations) {
          final isDetected = _isNoAIConfiguredError(variation);
          expect(isDetected, true, reason: 'Pattern "$pattern" should be detected in "$variation"');
        }
      }
    });
  });
}

// Test helper function that mimics the actual detection logic
bool _isNoAIConfiguredError(dynamic error) {
  final errorStr = error.toString().toLowerCase();
  return errorStr.contains('no enabled ai providers') ||
         errorStr.contains('no ai configured') ||
         errorStr.contains('no valid models') ||
         errorStr.contains('provider is disabled') ||
         (errorStr.contains('no') && errorStr.contains('provider'));
}
