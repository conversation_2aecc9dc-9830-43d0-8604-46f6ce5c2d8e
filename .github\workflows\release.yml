name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write

jobs:
  build-and-release:
    runs-on: ${{ matrix.runner }}
    
    strategy:
      matrix:
        include:
          - target: web
            runner: ubuntu-latest
          - target: android
            runner: ubuntu-latest
          - target: linux
            runner: ubuntu-latest
          - target: windows
            runner: windows-latest
          - target: macos
            runner: macos-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Java (Android)
      if: matrix.target == 'android'
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'

    - name: Install Linux dependencies
      if: matrix.target == 'linux'
      run: |
        sudo apt-get update -y
        sudo apt-get install -y ninja-build libgtk-3-dev libsqlite3-dev libsqlite3-0

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.1'
        channel: 'stable'
        cache: true

    - name: Install dependencies
      run: flutter pub get

    - name: Build Web
      if: matrix.target == 'web'
      run: |
        flutter build web --release
        cd build/web
        tar -czf ../../bervin-web-${GITHUB_REF#refs/tags/}.tar.gz .

    - name: Build Android
      if: matrix.target == 'android'
      run: |
        flutter build apk --release
        flutter build appbundle --release

    - name: Build Linux
      if: matrix.target == 'linux'
      run: |
        flutter build linux --release
        cd build/linux/x64/release/bundle
        tar -czf ../../../../../bervin-linux-${GITHUB_REF#refs/tags/}.tar.gz .

    - name: Build Windows
      if: matrix.target == 'windows'
      run: |
        flutter build windows --release
        Compress-Archive -Path build/windows/x64/runner/Release/* -DestinationPath bervin-windows-$($env:GITHUB_REF -replace 'refs/tags/','').zip

    - name: Build macOS
      if: matrix.target == 'macos'
      run: |
        flutter build macos --release
        cd build/macos/Build/Products/Release
        tar -czf ../../../../../bervin-macos-${GITHUB_REF#refs/tags/}.tar.gz .

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          bervin-web-*.tar.gz
          bervin-linux-*.tar.gz
          bervin-windows-*.zip
          bervin-macos-*.tar.gz
          build/app/outputs/flutter-apk/app-release.apk
          build/app/outputs/bundle/release/app-release.aab
        generate_release_notes: true
        draft: false
        prerelease: false
