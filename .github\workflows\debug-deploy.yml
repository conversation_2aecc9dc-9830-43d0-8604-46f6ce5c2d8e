name: Deploy to GitHub Pages (Debug)

on:
  workflow_dispatch:
    inputs:
      debug_mode:
        description: 'Enable debug mode'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  debug-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.1'
        channel: 'stable'
        cache: true

    - name: Flutter doctor
      run: flutter doctor -v

    - name: Check pubspec.yaml
      run: |
        echo "=== pubspec.yaml content ==="
        cat pubspec.yaml
        echo "=========================="

    - name: Install dependencies
      run: |
        flutter clean
        flutter pub get
        flutter pub deps

    - name: Check for analysis issues
      run: |
        echo "Running flutter analyze..."
        flutter analyze --no-fatal-infos || true

    - name: Run tests (allow failures)
      run: |
        echo "Running flutter test..."
        flutter test || true

    - name: List available web renderers
      run: |
        echo "Available Flutter web renderers:"
        flutter build web --help | grep -A 5 "web-renderer"

    - name: Build web app (verbose)
      run: |
        echo "Building web app..."
        flutter build web --release --verbose --web-renderer html --base-href "/Bervin-v2/"

    - name: Check build output
      run: |
        echo "=== Build directory contents ==="
        ls -la build/
        if [ -d "build/web" ]; then
          echo "=== Web build contents ==="
          ls -la build/web/
          echo "=== Index.html content (first 50 lines) ==="
          head -50 build/web/index.html
        else
          echo "ERROR: build/web directory not found!"
          exit 1
        fi

    - name: Upload debug artifact
      uses: actions/upload-artifact@v4
      with:
        name: debug-web-build
        path: build/web/
        retention-days: 1
