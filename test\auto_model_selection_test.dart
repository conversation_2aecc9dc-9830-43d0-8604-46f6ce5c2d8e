import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/chat_model_provider.dart';
import 'package:bervin/provider/settings_provider.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Auto Model Selection', () {
    test('should auto-select first available model when none is set', () {
      // This test would require mocking the SettingsProvider and SharedPreferences
      // For now, we'll test the logic conceptually
      
      // Mock available models
      final availableModels = [
        llm_model.Model(
          name: 'claude-3-sonnet',
          label: 'Claude 3 Sonnet',
          providerId: 'claude',
          icon: 'claude',
          providerName: 'Anthropic',
          apiStyle: 'claude',
        ),
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];

      // Test that the first model would be selected
      expect(availableModels.isNotEmpty, true);
      expect(availableModels.first.name, 'claude-3-sonnet');
    });

    test('should handle empty available models gracefully', () {
      final availableModels = <llm_model.Model>[];
      
      // Should not crash when no models are available
      expect(availableModels.isEmpty, true);
    });

    test('should validate model provider is enabled', () {
      final model = llm_model.Model(
        name: 'test-model',
        label: 'Test Model',
        providerId: 'test-provider',
        icon: 'test',
        providerName: 'Test Provider',
        apiStyle: 'test',
      );

      // Test model structure
      expect(model.providerId, 'test-provider');
      expect(model.name, 'test-model');
    });
  });
}
