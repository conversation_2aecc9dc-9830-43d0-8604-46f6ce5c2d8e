import 'package:flutter/material.dart';
import 'package:bervin/llm/model.dart' as llm_model;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bervin/provider/provider_manager.dart';
import 'package:logging/logging.dart';
import 'dart:convert';

class ChatModelProvider extends ChangeNotifier {
  static final ChatModelProvider _instance = ChatModelProvider._internal();
  factory ChatModelProvider() => _instance;
  ChatModelProvider._internal() {
    _loadSavedModel();
  }

  // Get the currently selected model
  static const String _modelKey = 'current_model';
  llm_model.Model _currentModel = llm_model.Model(
    name: "gpt-4o-mini",
    label: "GPT-4o-mini",
    providerId: "openai",
    icon: "openai",
    providerName: "OpenAI",
    apiStyle: "openai",
  );

  llm_model.Model get currentModel => _currentModel;

  set currentModel(llm_model.Model model) {
    // Validate that the provider for this model is enabled
    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(model.providerId);
    if (providerSetting == null) {
      Logger.root.warning('Attempted to select model ${model.name} from disabled provider ${model.providerId}');
      return; // Don't set the model if the provider is disabled
    }

    _currentModel = model;
    _saveSavedModel();
    notifyListeners();
  }

  Future<void> _loadSavedModel() async {
    final prefs = await SharedPreferences.getInstance();
    final modelName = prefs.getString(_modelKey) ?? "";
    if (modelName.isNotEmpty) {
      _currentModel = llm_model.Model.fromJson(jsonDecode(modelName));
    }
    notifyListeners();
  }

  Future<void> _saveSavedModel() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_modelKey, _currentModel.toString());
  }

  /// Validates current model and switches to an available one if current provider is disabled
  Future<void> validateAndSwitchModel() async {
    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(_currentModel.providerId);
    if (providerSetting != null) {
      return; // Current model is still valid
    }

    // Current model's provider is disabled, switch to first available model
    final availableModels = await ProviderManager.settingsProvider.getAvailableModels();
    if (availableModels.isNotEmpty) {
      Logger.root.info('Switching from disabled provider ${_currentModel.providerId} to ${availableModels.first.providerId}');
      _currentModel = availableModels.first;
      _saveSavedModel();
      notifyListeners();
    } else {
      Logger.root.warning('No available models found, keeping current model ${_currentModel.name}');
    }
  }
}
