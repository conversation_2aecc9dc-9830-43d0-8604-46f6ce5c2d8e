import 'package:flutter/material.dart';
import 'package:bervin/llm/model.dart' as llm_model;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bervin/provider/provider_manager.dart';
import 'package:logging/logging.dart';
import 'dart:convert';

class ChatModelProvider extends ChangeNotifier {
  static final ChatModelProvider _instance = ChatModelProvider._internal();
  factory ChatModelProvider() => _instance;
  ChatModelProvider._internal() {
    _loadSavedModel();
  }

  // Get the currently selected model
  static const String _modelKey = 'current_model';
  llm_model.Model? _currentModel;

  llm_model.Model get currentModel {
    // If no model is set, automatically select the first available configured one
    if (_currentModel == null) {
      _autoSelectFirstAvailableModel();
      // Schedule a notification for the next frame to avoid infinite loops
      if (_currentModel != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
    }

    // Fallback to a default if still null (shouldn't happen in normal cases)
    return _currentModel ?? llm_model.Model(
      name: "gpt-4o-mini",
      label: "GPT-4o-mini",
      providerId: "openai",
      icon: "openai",
      providerName: "OpenAI",
      apiStyle: "openai",
    );
  }

  set currentModel(llm_model.Model model) {
    // Validate that the provider for this model is enabled
    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(model.providerId);
    if (providerSetting == null) {
      Logger.root.warning('Attempted to select model ${model.name} from disabled provider ${model.providerId}');
      return; // Don't set the model if the provider is disabled
    }

    _currentModel = model;
    _saveSavedModel();
    notifyListeners();
  }

  /// Automatically selects the first available configured model
  void _autoSelectFirstAvailableModel() {
    final availableModels = ProviderManager.settingsProvider.availableModels;
    Logger.root.info('Auto-selecting model from ${availableModels.length} available models');

    if (availableModels.isNotEmpty) {
      _currentModel = availableModels.first;
      Logger.root.info('Auto-selected first available model: ${_currentModel!.name} from ${_currentModel!.providerName} (${_currentModel!.providerId})');
      _saveSavedModel();
      // Don't call notifyListeners here to avoid infinite loops during getter calls
    } else {
      Logger.root.warning('No available models found for auto-selection. Please configure at least one AI provider.');

      // List all provider settings for debugging
      final allSettings = ProviderManager.settingsProvider.apiSettings;
      Logger.root.info('Available provider settings: ${allSettings.length}');
      for (final setting in allSettings) {
        final isEnabled = setting.enable ?? true;
        Logger.root.info('  - ${setting.providerId}: enabled=$isEnabled, models=${setting.enabledModels?.length ?? 0}');
      }
    }
  }

  Future<void> _loadSavedModel() async {
    final prefs = await SharedPreferences.getInstance();
    final modelName = prefs.getString(_modelKey) ?? "";
    if (modelName.isNotEmpty) {
      try {
        _currentModel = llm_model.Model.fromJson(jsonDecode(modelName));

        // Validate that the loaded model's provider is still enabled
        final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(_currentModel!.providerId);
        if (providerSetting == null) {
          Logger.root.info('Loaded model ${_currentModel!.name} has disabled provider, will auto-select new one');
          _currentModel = null; // This will trigger auto-selection on next access
        }
      } catch (e) {
        Logger.root.warning('Failed to load saved model: $e');
        _currentModel = null; // This will trigger auto-selection on next access
      }
    }
    notifyListeners();
  }

  Future<void> _saveSavedModel() async {
    if (_currentModel != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_modelKey, _currentModel!.toString());
    }
  }

  /// Validates current model and switches to an available one if current provider is disabled
  Future<void> validateAndSwitchModel() async {
    // If no model is currently set, auto-select one
    if (_currentModel == null) {
      Logger.root.info('No current model set, triggering auto-selection');
      _autoSelectFirstAvailableModel();
      notifyListeners();
      return;
    }

    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(_currentModel!.providerId);
    if (providerSetting != null) {
      return; // Current model is still valid
    }

    // Current model's provider is disabled, switch to first available model
    final availableModels = await ProviderManager.settingsProvider.getAvailableModels();
    if (availableModels.isNotEmpty) {
      Logger.root.info('Switching from disabled provider ${_currentModel!.providerId} to ${availableModels.first.providerId}');
      _currentModel = availableModels.first;
      _saveSavedModel();
      notifyListeners();
    } else {
      Logger.root.warning('No available models found, keeping current model ${_currentModel!.name}');
    }
  }

  /// Manually trigger auto-selection of the first available model
  /// This can be called from UI when user wants to auto-select a model
  void autoSelectModel() {
    _autoSelectFirstAvailableModel();
    notifyListeners();
  }

  /// Force a UI update - useful when model selection might be out of sync
  void refreshUI() {
    notifyListeners();
  }
}
