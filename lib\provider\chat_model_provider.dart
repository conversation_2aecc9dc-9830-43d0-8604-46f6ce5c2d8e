import 'package:flutter/material.dart';
import 'package:bervin/llm/model.dart' as llm_model;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bervin/provider/provider_manager.dart';
import 'package:logging/logging.dart';
import 'dart:convert';

class ChatModelProvider extends ChangeNotifier {
  static final ChatModelProvider _instance = ChatModelProvider._internal();
  factory ChatModelProvider() => _instance;
  ChatModelProvider._internal() {
    _loadSavedModel();
  }

  // Get the currently selected model
  static const String _modelKey = 'current_model';
  llm_model.Model? _currentModel;

  llm_model.Model get currentModel {
    // If no model is set, automatically select the first available configured one
    if (_currentModel == null) {
      _autoSelectFirstAvailableModel();
      // Schedule a notification for the next frame to avoid infinite loops
      if (_currentModel != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          notifyListeners();
        });
      }
    }

    // Fallback: if still null, try to get any available model or return a placeholder
    if (_currentModel == null) {
      final availableModels = ProviderManager.settingsProvider.availableModels;
      final validModels = availableModels.where((model) => !_isModelDeprecated(model)).toList();

      if (validModels.isNotEmpty) {
        Logger.root.info('Using first valid model as fallback: ${validModels.first.name}');
        return validModels.first;
      } else {
        // Return a placeholder model that indicates no valid models are available
        Logger.root.warning('No valid models available, returning placeholder');
        return llm_model.Model(
          name: "",
          label: "No AI configured",
          providerId: "",
          icon: "warning",
          providerName: "",
          apiStyle: "",
        );
      }
    }

    return _currentModel!;
  }

  set currentModel(llm_model.Model model) {
    // Validate that the provider for this model is enabled
    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(model.providerId);
    if (providerSetting == null) {
      Logger.root.warning('Attempted to select model ${model.name} from disabled provider ${model.providerId}');
      return; // Don't set the model if the provider is disabled
    }

    _currentModel = model;
    _saveSavedModel();
    notifyListeners();
  }

  /// Check if a model is deprecated (specifically Gemini deprecated models)
  bool _isModelDeprecated(llm_model.Model model) {
    final modelName = model.name.toLowerCase();

    // Check for known deprecated Gemini models
    if (model.providerId == 'gemini') {
      final deprecatedGeminiModels = [
        'gemini-1.0-pro',
        'gemini-1.0-pro-vision',
        'gemini-1.0-pro-vision-latest',
        'gemini-pro',
        'gemini-pro-vision',
      ];

      return deprecatedGeminiModels.any((deprecated) => modelName.contains(deprecated));
    }

    return false;
  }

  /// Automatically selects the first available configured model
  void _autoSelectFirstAvailableModel() {
    final availableModels = ProviderManager.settingsProvider.availableModels;
    Logger.root.info('Auto-selecting model from ${availableModels.length} available models');

    if (availableModels.isNotEmpty) {
      // Filter out deprecated models
      final validModels = availableModels.where((model) => !_isModelDeprecated(model)).toList();

      if (validModels.isNotEmpty) {
        _currentModel = validModels.first;
        Logger.root.info('Auto-selected first valid model: ${_currentModel!.name} from ${_currentModel!.providerName} (${_currentModel!.providerId})');
        _saveSavedModel();
      } else {
        Logger.root.warning('All available models are deprecated. Available models:');
        for (final model in availableModels) {
          Logger.root.warning('  - ${model.name} (${model.providerId}) - deprecated: ${_isModelDeprecated(model)}');
        }
        _currentModel = null;
      }
      // Don't call notifyListeners here to avoid infinite loops during getter calls
    } else {
      Logger.root.warning('No available models found for auto-selection. Please configure at least one AI provider.');

      // List all provider settings for debugging
      final allSettings = ProviderManager.settingsProvider.apiSettings;
      Logger.root.info('Available provider settings: ${allSettings.length}');
      for (final setting in allSettings) {
        final isEnabled = setting.enable ?? true;
        Logger.root.info('  - ${setting.providerId}: enabled=$isEnabled, models=${setting.enabledModels?.length ?? 0}');
      }
    }
  }

  Future<void> _loadSavedModel() async {
    final prefs = await SharedPreferences.getInstance();
    final modelName = prefs.getString(_modelKey) ?? "";
    if (modelName.isNotEmpty) {
      try {
        _currentModel = llm_model.Model.fromJson(jsonDecode(modelName));

        // Validate that the loaded model is not deprecated
        if (_isModelDeprecated(_currentModel!)) {
          Logger.root.info('Loaded model ${_currentModel!.name} is deprecated, clearing and will auto-select new one');
          _currentModel = null;
          // Clear the saved deprecated model
          await prefs.remove(_modelKey);
        } else {
          // Validate that the loaded model's provider is still enabled
          final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(_currentModel!.providerId);
          if (providerSetting == null) {
            Logger.root.info('Loaded model ${_currentModel!.name} has disabled provider, will auto-select new one');
            _currentModel = null; // This will trigger auto-selection on next access
          }
        }
      } catch (e) {
        Logger.root.warning('Failed to load saved model: $e');
        _currentModel = null; // This will trigger auto-selection on next access
      }
    }
    notifyListeners();
  }

  Future<void> _saveSavedModel() async {
    if (_currentModel != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_modelKey, _currentModel!.toString());
    }
  }

  /// Validates current model and switches to an available one if current provider is disabled
  Future<void> validateAndSwitchModel() async {
    // If no model is currently set, auto-select one
    if (_currentModel == null) {
      Logger.root.info('No current model set, triggering auto-selection');
      _autoSelectFirstAvailableModel();
      notifyListeners();
      return;
    }

    final providerSetting = ProviderManager.settingsProvider.getEnabledProviderSetting(_currentModel!.providerId);
    if (providerSetting != null) {
      return; // Current model is still valid
    }

    // Current model's provider is disabled, switch to first available model
    final availableModels = await ProviderManager.settingsProvider.getAvailableModels();
    if (availableModels.isNotEmpty) {
      Logger.root.info('Switching from disabled provider ${_currentModel!.providerId} to ${availableModels.first.providerId}');
      _currentModel = availableModels.first;
      _saveSavedModel();
      notifyListeners();
    } else {
      Logger.root.warning('No available models found, keeping current model ${_currentModel!.name}');
    }
  }

  /// Manually trigger auto-selection of the first available model
  /// This can be called from UI when user wants to auto-select a model
  void autoSelectModel() {
    _autoSelectFirstAvailableModel();
    notifyListeners();
  }

  /// Force a UI update - useful when model selection might be out of sync
  void refreshUI() {
    notifyListeners();
  }

  /// Clear deprecated models and force reselection
  Future<void> clearDeprecatedModel() async {
    if (_currentModel != null && _isModelDeprecated(_currentModel!)) {
      Logger.root.info('Clearing deprecated model: ${_currentModel!.name}');
      _currentModel = null;

      // Clear from saved preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_modelKey);

      // Trigger auto-selection
      _autoSelectFirstAvailableModel();
      notifyListeners();
    }
  }
}
