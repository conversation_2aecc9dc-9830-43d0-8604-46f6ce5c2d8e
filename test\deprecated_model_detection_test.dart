import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Deprecated Model Detection', () {
    test('should detect deprecated Gemini models', () {
      final deprecatedModels = [
        llm_model.Model(
          name: 'gemini-1.0-pro',
          label: 'Gemini 1.0 Pro',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gemini-1.0-pro-vision-latest',
          label: 'Gemini 1.0 Pro Vision Latest',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gemini-pro',
          label: 'Gemini Pro',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gemini-pro-vision',
          label: 'Gemini Pro Vision',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
      ];

      for (final model in deprecatedModels) {
        final isDeprecated = _isModelDeprecated(model);
        expect(isDeprecated, true, reason: 'Model ${model.name} should be detected as deprecated');
      }
    });

    test('should not detect valid Gemini models as deprecated', () {
      final validModels = [
        llm_model.Model(
          name: 'gemini-1.5-pro',
          label: 'Gemini 1.5 Pro',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gemini-1.5-flash',
          label: 'Gemini 1.5 Flash',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
      ];

      for (final model in validModels) {
        final isDeprecated = _isModelDeprecated(model);
        expect(isDeprecated, false, reason: 'Model ${model.name} should not be detected as deprecated');
      }
    });

    test('should not detect non-Gemini models as deprecated', () {
      final nonGeminiModels = [
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
        llm_model.Model(
          name: 'claude-3-sonnet',
          label: 'Claude 3 Sonnet',
          providerId: 'claude',
          icon: 'claude',
          providerName: 'Anthropic',
          apiStyle: 'claude',
        ),
      ];

      for (final model in nonGeminiModels) {
        final isDeprecated = _isModelDeprecated(model);
        expect(isDeprecated, false, reason: 'Non-Gemini model ${model.name} should not be detected as deprecated');
      }
    });

    test('should filter deprecated models from available list', () {
      final allModels = [
        llm_model.Model(
          name: 'gemini-1.0-pro-vision-latest',
          label: 'Gemini 1.0 Pro Vision Latest',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gemini-1.5-pro',
          label: 'Gemini 1.5 Pro',
          providerId: 'gemini',
          icon: 'gemini',
          providerName: 'Google',
          apiStyle: 'gemini',
        ),
        llm_model.Model(
          name: 'gpt-4',
          label: 'GPT-4',
          providerId: 'openai',
          icon: 'openai',
          providerName: 'OpenAI',
          apiStyle: 'openai',
        ),
      ];

      final validModels = allModels.where((model) => !_isModelDeprecated(model)).toList();

      expect(validModels.length, 2);
      expect(validModels.any((m) => m.name == 'gemini-1.0-pro-vision-latest'), false);
      expect(validModels.any((m) => m.name == 'gemini-1.5-pro'), true);
      expect(validModels.any((m) => m.name == 'gpt-4'), true);
    });
  });
}

// Copy of the detection logic for testing
bool _isModelDeprecated(llm_model.Model model) {
  final modelName = model.name.toLowerCase();
  
  // Check for known deprecated Gemini models
  if (model.providerId == 'gemini') {
    final deprecatedGeminiModels = [
      'gemini-1.0-pro',
      'gemini-1.0-pro-vision',
      'gemini-1.0-pro-vision-latest',
      'gemini-pro',
      'gemini-pro-vision',
    ];
    
    return deprecatedGeminiModels.any((deprecated) => modelName.contains(deprecated));
  }
  
  return false;
}
