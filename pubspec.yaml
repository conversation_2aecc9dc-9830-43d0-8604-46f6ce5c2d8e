name: bervin
description: "<PERSON><PERSON> <PERSON> <PERSON> Chat with Mcp"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 0.0.70

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2
  shared_preferences: ^2.2.2
  http: ^1.2.0
  eventflux: ^2.2.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  synchronized: ^3.3.0+3
  provider: ^6.1.2
  logging: ^1.2.0
  path_provider: ^2.1.5
  path: ^1.9.0
  sqflite: ^2.3.0
  sqflite_common_ffi: ^2.3.2
  markdown: ^7.3.0
  
  # Firebase Authentication
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  google_sign_in: ^6.2.1
  cloud_firestore: ^5.4.3
  url_launcher: ^6.3.1
  window_manager: ^0.5.0
  flutter_keyboard_visibility: ^6.0.0
  file_picker: ^10.1.9
  mime: ^2.0.0
  uuid: ^4.5.1
  keyboard_dismisser: ^3.0.0
  markdown_widget: ^2.3.2+6
  flutter_math_fork: ^0.7.3
  flutter_highlight: ^0.7.0
  share_plus: ^11.0.0
  screenshot: ^3.0.0
  flutter_inappwebview: ^6.1.5
  cached_network_image: ^3.4.1
  html2md: ^1.3.2
  event_bus: ^2.0.1
  flutter_cache_manager: ^3.3.1
  flutter_svg: ^2.0.17
  json_serializable: ^6.9.5
  flutter_switch: ^0.3.2
  flutter_popup: ^3.3.4
  flutter_form_builder: ^10.0.1
  flutter_client_sse: ^2.0.3
  package_info_plus: ^8.3.0
  bot_toast: ^4.1.3
  sqflite_common_ffi_web: ^1.0.0
  # 网络服务相关
  shelf: ^1.4.1  # HTTP服务器
  shelf_router: ^1.1.4  # 路由
  shelf_cors_headers: ^0.1.5  # CORS支持
  network_info_plus: ^5.0.0  # 获取网络信息
  qr_flutter: ^4.1.0  # 二维码生成
  # qr_code_scanner: ^1.0.1  # 二维码扫描 - 已弃用，有namespace问题
  mobile_scanner: ^7.0.1  # 替代qr_code_scanner的现代二维码扫描器
  dio: ^5.4.0  # HTTP客户端

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

  test: ^1.24.0
  mockito: ^5.4.0
  build_runner: ^2.4.0
  flutter_launcher_icons: ^0.14.3


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/mcp_server.json
    - assets/logo.png
    - assets/sandbox/
    - assets/logo/
    - assets/logo_256.png

flutter_launcher_icons:
  remove_alpha_ios: true
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo.png"
  min_sdk_android: 21 # Android最低SDK版本
  web:
    generate: true
    image_path: "assets/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/logo.png"
    icon_size: 48 # 可选，默认为48
  macos:
    generate: true
    image_path: "assets/logo.png"
  linux:
    generate: true
    image_path: "assets/logo_256.png"
