import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Error Simplification', () {
    test('should detect API key errors correctly', () {
      final apiKeyErrors = [
        'Invalid API key provided',
        'API key missing',
        'Authentication failed',
        'Unauthorized access',
        '401 Unauthorized',
        'Forbidden API access',
        'API key unauthorized',
      ];

      for (final error in apiKeyErrors) {
        final isAPIKeyError = _isAPIKeyError(error);
        expect(isAPIKeyError, true, reason: 'Should detect API key error: $error');
      }
    });

    test('should detect invalid request errors correctly', () {
      final invalidRequestErrors = [
        'Invalid request format',
        'Bad request received',
        'Malformed request body',
        'Invalid parameter value',
        'Invalid input provided',
        'Request validation failed',
        '400 Bad Request error',
      ];

      for (final error in invalidRequestErrors) {
        final isInvalidRequest = _isInvalidRequestError(error);
        expect(isInvalidRequest, true, reason: 'Should detect invalid request: $error');
      }
    });

    test('should detect quota exceeded errors correctly', () {
      final quotaErrors = [
        'API quota exceeded',
        'Rate limit exceeded',
        'Resource exhausted',
        'Billing account suspended',
        'Insufficient quota remaining',
        'Usage limit reached',
        'Credit balance insufficient',
        'Payment required for continued access',
      ];

      for (final error in quotaErrors) {
        final isQuotaError = _isQuotaExceededError(error);
        expect(isQuotaError, true, reason: 'Should detect quota error: $error');
      }
    });

    test('should detect deprecated model errors correctly', () {
      final deprecatedErrors = [
        'Model gemini-1.0-pro is deprecated',
        'gemini-pro-vision is no longer supported',
        'This model version is deprecated',
        'Model not supported anymore',
      ];

      for (final error in deprecatedErrors) {
        final isDeprecated = _isDeprecatedModelError(error);
        expect(isDeprecated, true, reason: 'Should detect deprecated model: $error');
      }
    });

    test('should detect no AI configured errors correctly', () {
      final noAIErrors = [
        'No AI model configured',
        'No provider selected',
        'Please configure an AI provider',
        'No model available',
      ];

      for (final error in noAIErrors) {
        final isNoAI = _isNoAIConfiguredError(error);
        expect(isNoAI, true, reason: 'Should detect no AI error: $error');
      }
    });

    test('should not misclassify errors', () {
      final testCases = [
        {
          'error': 'Network connection failed',
          'shouldNotBe': ['api_key', 'invalid_request', 'quota', 'deprecated', 'no_ai'],
        },
        {
          'error': 'Server internal error',
          'shouldNotBe': ['api_key', 'invalid_request', 'quota', 'deprecated', 'no_ai'],
        },
        {
          'error': 'Timeout occurred',
          'shouldNotBe': ['api_key', 'invalid_request', 'quota', 'deprecated', 'no_ai'],
        },
      ];

      for (final testCase in testCases) {
        final error = testCase['error'] as String;
        final shouldNotBe = testCase['shouldNotBe'] as List<String>;

        for (final errorType in shouldNotBe) {
          switch (errorType) {
            case 'api_key':
              expect(_isAPIKeyError(error), false, reason: '$error should not be API key error');
              break;
            case 'invalid_request':
              expect(_isInvalidRequestError(error), false, reason: '$error should not be invalid request');
              break;
            case 'quota':
              expect(_isQuotaExceededError(error), false, reason: '$error should not be quota error');
              break;
            case 'deprecated':
              expect(_isDeprecatedModelError(error), false, reason: '$error should not be deprecated error');
              break;
            case 'no_ai':
              expect(_isNoAIConfiguredError(error), false, reason: '$error should not be no AI error');
              break;
          }
        }
      }
    });

    test('should handle case insensitive error detection', () {
      final testCases = [
        {
          'error': 'INVALID API KEY PROVIDED',
          'type': 'api_key',
        },
        {
          'error': 'Bad Request Format',
          'type': 'invalid_request',
        },
        {
          'error': 'QUOTA EXCEEDED',
          'type': 'quota',
        },
      ];

      for (final testCase in testCases) {
        final error = testCase['error'] as String;
        final type = testCase['type'] as String;

        switch (type) {
          case 'api_key':
            expect(_isAPIKeyError(error), true, reason: 'Should detect case insensitive: $error');
            break;
          case 'invalid_request':
            expect(_isInvalidRequestError(error), true, reason: 'Should detect case insensitive: $error');
            break;
          case 'quota':
            expect(_isQuotaExceededError(error), true, reason: 'Should detect case insensitive: $error');
            break;
        }
      }
    });

    test('should prioritize error types correctly', () {
      // API key errors should be detected before invalid request
      final apiKeyError = 'Invalid API key in request';
      expect(_isAPIKeyError(apiKeyError), true);
      expect(_isInvalidRequestError(apiKeyError), true); // This might also match
      
      // But API key should be handled first in the error handling flow
      
      // Quota errors should be specific
      final quotaError = 'API quota exceeded for billing account';
      expect(_isQuotaExceededError(quotaError), true);
      expect(_isAPIKeyError(quotaError), false);
    });

    test('should handle complex error messages', () {
      final complexErrors = [
        {
          'error': 'DeepSeek API call failed: Invalid request - The model parameter is required but was not provided in the request body. Please check your API documentation.',
          'expectedType': 'invalid_request',
        },
        {
          'error': 'OpenAI API Error: Your API key is invalid or has been revoked. Please check your API key in the dashboard.',
          'expectedType': 'api_key',
        },
        {
          'error': 'Gemini API quota exceeded: You have reached your monthly usage limit. Please upgrade your billing plan or wait until next month.',
          'expectedType': 'quota',
        },
      ];

      for (final testCase in complexErrors) {
        final error = testCase['error'] as String;
        final expectedType = testCase['expectedType'] as String;

        switch (expectedType) {
          case 'api_key':
            expect(_isAPIKeyError(error), true, reason: 'Should detect API key in complex error: $error');
            break;
          case 'invalid_request':
            expect(_isInvalidRequestError(error), true, reason: 'Should detect invalid request in complex error: $error');
            break;
          case 'quota':
            expect(_isQuotaExceededError(error), true, reason: 'Should detect quota in complex error: $error');
            break;
        }
      }
    });

    test('should handle empty and null errors gracefully', () {
      expect(_isAPIKeyError(''), false);
      expect(_isInvalidRequestError(''), false);
      expect(_isQuotaExceededError(''), false);
      expect(_isDeprecatedModelError(''), false);
      expect(_isNoAIConfiguredError(''), false);
    });
  });
}

// Test helper functions that mimic the actual implementation

bool _isAPIKeyError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('invalid api key') ||
         errorStr.contains('api key') && (errorStr.contains('invalid') || errorStr.contains('missing') || errorStr.contains('unauthorized')) ||
         errorStr.contains('authentication failed') ||
         errorStr.contains('unauthorized') ||
         errorStr.contains('401') ||
         errorStr.contains('forbidden') && errorStr.contains('api');
}

bool _isInvalidRequestError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('invalid request') ||
         errorStr.contains('bad request') ||
         errorStr.contains('malformed request') ||
         errorStr.contains('invalid parameter') ||
         errorStr.contains('invalid input') ||
         errorStr.contains('request validation failed') ||
         (errorStr.contains('400') && errorStr.contains('error'));
}

bool _isQuotaExceededError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('quota') ||
         errorStr.contains('resource_exhausted') ||
         errorStr.contains('rate limit') ||
         errorStr.contains('billing') ||
         errorStr.contains('exceeded your current quota') ||
         errorStr.contains('insufficient_quota') ||
         errorStr.contains('usage limit') ||
         errorStr.contains('credit') ||
         errorStr.contains('payment');
}

bool _isDeprecatedModelError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('deprecated') ||
         errorStr.contains('no longer supported') ||
         errorStr.contains('not supported anymore');
}

bool _isNoAIConfiguredError(String error) {
  final errorStr = error.toLowerCase();
  return errorStr.contains('no ai') ||
         errorStr.contains('no model') ||
         errorStr.contains('no provider') ||
         errorStr.contains('configure') && errorStr.contains('ai');
}
